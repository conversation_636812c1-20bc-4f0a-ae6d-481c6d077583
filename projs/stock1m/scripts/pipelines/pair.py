"""
Pair Pipeline - 个股时间序列分析

适用场景：
- LSTM、Transformer、个股预测
- 输出: [B, T, F] -> [B, T, 1]
- 固定形状，可正常batch
- 使用seq2seq单向GRU模型
"""

import os
import sys
import argparse
import numpy as np
import torch
import torch.nn as nn
from dataclasses import dataclass
from loguru import logger
from torch.utils.data.distributed import DistributedSampler
import time

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../../..'))

from DLlib.data.cube import MinuteCube
from DLlib.data.field_spec import FieldSpec
from DLlib.data.standardizer import CubeStandardizer
from DLlib.data.datasets import TensorPairDataset
from DLlib.data.ddp_utils import set_global_seed, setup_dist, cleanup_dist, is_main_process
from DLlib.data.folds import rolling_splits_by_years, get_fold_info

from DLlib.train import Model<PERSON>rainer, VectorLoss, rmse, ic_cs, Wandb<PERSON>racker


@dataclass
class Config:
    """Pair Pipeline 配置"""
    # 实验名称
    exp_name: str = f"test{time.strftime('%m%d')}"

    # 数据配置
    data_path: str = "projs/stock1m/data/OHLCVA_Vwap_cube.npy"
    n_feat: int = 7
    n_label: int = 1
    n_weight: int = 0

    # 滚动配置（61天小样本，2天=1年）
    train_years: float = 3.0   # 6天训练
    test_years: float = 1.0    # 2天测试
    days_per_year: int = 2     # 2天=1年，61天=30.5年
    inner_val_ratio: float = 0.2

    # 模型配置
    hidden: int = 128
    layers: int = 1
    dropout: float = 0.1

    # 训练配置
    lr: float = 1e-4
    weight_decay: float = 1e-4
    clip: float = 1.0
    epochs: int = 20
    early_stop: int = 10
    batch_size: int = 64  # Pair可以使用较大的batch size

    # 损失和指标配置
    loss_type: str = "rmse"
    primary_metric: str = "rmse"
    primary_higher_better: bool = False

    # 系统配置
    seed: int = 666
    num_workers: int = 2
    accum_steps: int = 1
    ckpt_root: str = "projs/stock1m/checkpoints"

    # Wandb配置
    wandb_project: str = "tslib"

    wandb_group: str = "stock1m_pair"
    wandb_mode: str = "online"
    wandb_dir: str = "projs/stock1m/logs/wandb"
    wandb_tags: list = None

    # 模型信息
    model_name: str = "GRUSeq"
    model_type: str = "seq2seq"

    def __post_init__(self):
        if self.wandb_tags is None:
            self.wandb_tags = ["PairDataset", "GRU", "seq2seq", "个股分析"]


class GRUSeq(nn.Module):
    """seq2seq单向GRU模型（与DaySection相同）"""
    
    def __init__(self, f_in: int, hidden: int = 256, layers: int = 2, dropout: float = 0.1):
        super().__init__()
        self.gru = nn.GRU(
            input_size=f_in, 
            hidden_size=hidden, 
            num_layers=layers, 
            batch_first=True, 
            bidirectional=False,
            dropout=dropout if layers > 1 else 0
        )
        self.head = nn.Linear(hidden, 1)
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, x):
        """
        前向传播
        
        参数:
            x: [B, T, F]
        
        返回:
            [B, T, 1]
        """
        out, _ = self.gru(x)  # [B, T, hidden]
        out = self.dropout(out)
        return self.head(out)  # [B, T, 1]


def run(args=None):
    """运行Pair Pipeline"""
    cfg = Config()
    if args is not None and args.data_path:
        cfg.data_path = args.data_path

    # 设置全局随机种子
    set_global_seed(cfg.seed)

    # 设置分布式环境
    _, device = setup_dist()

    # 加载特征数据
    X = np.load(cfg.data_path)  # [F, D, T, N]
    F, D, T, N = X.shape

    # 加载y1数据并拼接
    y1_path = "/disk4/shared/intern/laiyc/forModel/y1.npy"
    if os.path.exists(y1_path):
        y1 = np.load(y1_path, mmap_mode="r")  # [1, D, T, N]
        if is_main_process():
            logger.info(f"加载y1数据: {y1.shape}")

        # 拼接X和y1
        X = np.concatenate([X, y1], axis=0)  # [F+1, D, T, N]
        F = X.shape[0]  # 更新F

        if is_main_process():
            logger.info(f"拼接后数据形状: F={F}, D={D}, T={T}, N={N}")
    else:
        if is_main_process():
            logger.warning(f"y1数据文件不存在: {y1_path}，使用原始数据")

    if is_main_process():
        logger.info(f"=== Pair Pipeline ===")
        logger.info(f"最终数据形状: F={F}, D={D}, T={T}, N={N}")

    # 生成滚动切分
    folds = list(rolling_splits_by_years(
        D=D, 
        train_years=cfg.train_years, 
        test_years=cfg.test_years,
        days_per_year=cfg.days_per_year, 
        inner_val_ratio=cfg.inner_val_ratio,
    ))
    
    if is_main_process():
        fold_info = get_fold_info(D, cfg.train_years, cfg.test_years, cfg.days_per_year)
        logger.info(f"滚动配置: {cfg.train_years}年训练 + {cfg.test_years}年测试")
        logger.info(f"Fold信息: {fold_info}")

    spec = FieldSpec(n_feat=cfg.n_feat, n_label=cfg.n_label, n_weight=cfg.n_weight)

    # 初始化Wandb跟踪器
    tracker = WandbTracker(
        project=cfg.wandb_project,
        entity=cfg.wandb_entity,
        run_name=f"pair-{cfg.model_name}-rolling-{cfg.train_years}x{cfg.test_years}",
        group=cfg.wandb_group,
        mode=cfg.wandb_mode,
        dir_=cfg.wandb_dir,
        tags=cfg.wandb_tags,
        config={
            "pipeline": "Pair",
            "model": cfg.model_name,
            "hidden": cfg.hidden,
            "layers": cfg.layers,
            "dropout": cfg.dropout,
            "batch_size": cfg.batch_size,
            "loss_type": cfg.loss_type,
            "primary_metric": cfg.primary_metric,
            "optim.lr": cfg.lr,
            "optim.wd": cfg.weight_decay,
            "clip": cfg.clip,
            "epochs": cfg.epochs,
            "rolling": f"{cfg.train_years}x{cfg.test_years} years, val_ratio={cfg.inner_val_ratio}",
            "data_shape": [F, D, T, N],
            "num_folds": len(folds),
        },
    )

    # 开始滚动训练
    fold_results = []  # 存储所有fold的结果
    for fid, (idx_tr, idx_va, idx_te) in enumerate(folds):
        if is_main_process():
            logger.info(f"[Fold {fid}] 开始训练")
            logger.info(f"  训练: {len(idx_tr)}天, 验证: {len(idx_va)}天, 测试: {len(idx_te)}天")

        # 1. 切分数据
        X_tr = X[:, idx_tr, :, :]
        X_va = X[:, idx_va, :, :]
        X_te = X[:, idx_te, :, :]

        # 2. 构造cube并标准化
        cube_tr = MinuteCube.from_fdtN(X_tr)
        std = CubeStandardizer(mode="per_stock_f").fit(cube_tr)

        cube_tr = std.transform(cube_tr)
        cube_va = std.transform(MinuteCube.from_fdtN(X_va))
        cube_te = std.transform(MinuteCube.from_fdtN(X_te))

        # 3. 创建数据集和采样器（使用官方DistributedSampler）
        ds_tr = TensorPairDataset(cube_tr, spec)
        ds_va = TensorPairDataset(cube_va, spec)
        ds_te = TensorPairDataset(cube_te, spec)

        sampler_tr = DistributedSampler(ds_tr, shuffle=True, drop_last=False)
        sampler_va = DistributedSampler(ds_va, shuffle=False, drop_last=False)
        sampler_te = DistributedSampler(ds_te, shuffle=False, drop_last=False)

        dl_tr = torch.utils.data.DataLoader(
            ds_tr, batch_size=cfg.batch_size, sampler=sampler_tr,
            num_workers=cfg.num_workers, pin_memory=True, persistent_workers=True
        )
        dl_va = torch.utils.data.DataLoader(
            ds_va, batch_size=cfg.batch_size, sampler=sampler_va,
            num_workers=cfg.num_workers, pin_memory=True, persistent_workers=True
        )
        dl_te = torch.utils.data.DataLoader(
            ds_te, batch_size=cfg.batch_size, sampler=sampler_te,
            num_workers=cfg.num_workers, pin_memory=True, persistent_workers=True
        )

        # 4. 创建模型和优化器
        model = GRUSeq(cfg.n_feat, cfg.hidden, cfg.layers, cfg.dropout).to(device)
        optimizer = torch.optim.Adam(model.parameters(), lr=cfg.lr, weight_decay=cfg.weight_decay)

        # 5. 定义损失函数和指标
        loss_fn = VectorLoss(cfg.loss_type, pred_dim=1, label_dim=1)
        metrics = {
            "rmse": lambda p, y, w: rmse(p, y, w),
        }

        # 6. 创建训练器
        trainer = ModelTrainer(
            model=model,
            optimizer=optimizer,
            loss_fn=loss_fn,
            metrics=metrics,
            primary_metric=cfg.primary_metric,
            primary_higher_better=cfg.primary_higher_better,
            device=device,
            use_ddp=True,
            grad_clip_norm=cfg.clip,
            grad_accum_steps=cfg.accum_steps,
            scheduler=None,
            early_stop_patience=cfg.early_stop,
            tracker=tracker,
        )

        # 7. 训练模型
        fold_dir = os.path.join(cfg.ckpt_root, f"pair_fold{fid:02d}")
        os.makedirs(fold_dir, exist_ok=True)
        best_path = os.path.join(fold_dir, "best.pt")

        trainer.fit(dl_tr, dl_va, num_epochs=cfg.epochs, save_path=best_path)

        # 8. 测试阶段
        if is_main_process():
            model.load_state_dict(torch.load(best_path, map_location=device))
            model.eval()

            preds = []
            labels = []
            with torch.no_grad():
                for batch in dl_te:
                    x = batch["features"].to(device)
                    y = batch["label"].cpu().numpy()
                    y_hat = model(x).detach().cpu().numpy()  # [B, T, 1]
                    preds.append(y_hat)
                    labels.append(y)

            # 保存预测结果
            preds = np.concatenate(preds, axis=0)  # [Total_pairs, T, 1]
            labels = np.concatenate(labels, axis=0)

            pred_path = os.path.join(fold_dir, "test_preds.npy")
            label_path = os.path.join(fold_dir, "test_labels.npy")
            np.save(pred_path, preds)
            np.save(label_path, labels)

            # 计算测试集指标
            test_metrics = {}

            # 计算所有配置的指标
            dummy_weight = np.ones_like(labels)
            for metric_name, metric_fn in metrics.items():
                try:
                    pred_tensor = torch.from_numpy(preds).float()
                    label_tensor = torch.from_numpy(labels).float()
                    weight_tensor = torch.from_numpy(dummy_weight).float()

                    metric_value = float(metric_fn(pred_tensor, label_tensor, weight_tensor).item())
                    test_metrics[f"test_{metric_name}"] = metric_value
                except Exception as e:
                    logger.warning(f"计算指标 {metric_name} 失败: {e}")

            # 添加数据统计
            test_metrics.update({
                "test_pairs": len(preds),
                "train_pairs": len(ds_tr),
                "valid_pairs": len(ds_va),
            })

            # 记录单个fold结果 - 使用新的简化方式
            tracker.log_metrics({f"fold_{fid}": test_metrics.get(f"test_{cfg.primary_metric}", 0.0)})
            # tracker.save_artifact(pred_path, type_="predictions")

            # 存储fold结果用于最终汇总
            fold_results.append(test_metrics.get(f"test_{cfg.primary_metric}", 0.0))

            # 输出主要指标
            main_metric_value = test_metrics.get(f"test_{cfg.primary_metric}", "N/A")
            logger.info(f"[Fold {fid}] 完成 - 测试{cfg.primary_metric.upper()}: {main_metric_value:.6f}")

    # 记录整体汇总结果
    if fold_results and is_main_process():
        cv_mean = sum(fold_results) / len(fold_results)
        cv_std = (sum((x - cv_mean) ** 2 for x in fold_results) / len(fold_results)) ** 0.5
        tracker.log_metrics({
            "cv_mean": cv_mean,
            "cv_std": cv_std,
            "cv_folds": len(fold_results)
        })
        logger.info(f"CV总结 - 平均{cfg.primary_metric.upper()}: {cv_mean:.6f} ± {cv_std:.6f}")

    # 结束实验
    tracker.finish()
    cleanup_dist()


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Pair Pipeline")
    parser.add_argument("--data_path", type=str, default="projs/stock1m/data/OHLCVA_Vwap_cube.npy")
    args = parser.parse_args()
    run(args)
