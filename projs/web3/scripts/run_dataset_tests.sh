#!/bin/bash

# 测试TensorDaySectionDataset加载脚本
# 包含单卡版本和DDP版本

set -e

# 默认数据路径
DATA_PATH="/disk4/shared/intern/laiyc/forModel/alpha98.npy"

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --data_path)
            DATA_PATH="$2"
            shift 2
            ;;
        --single)
            RUN_SINGLE=true
            shift
            ;;
        --ddp)
            RUN_DDP=true
            shift
            ;;
        --help|-h)
            echo "用法: $0 [选项]"
            echo "选项:"
            echo "  --data_path PATH    数据文件路径 (默认: $DATA_PATH)"
            echo "  --single           只运行单卡测试"
            echo "  --ddp              只运行DDP测试"
            echo "  --help, -h         显示此帮助信息"
            echo ""
            echo "如果不指定 --single 或 --ddp，将依次运行两个测试"
            exit 0
            ;;
        *)
            echo "未知参数: $1"
            echo "使用 --help 查看帮助"
            exit 1
            ;;
    esac
done

# 如果没有指定运行哪个测试，则运行所有测试
if [[ -z "$RUN_SINGLE" && -z "$RUN_DDP" ]]; then
    RUN_SINGLE=true
    RUN_DDP=true
fi

echo "=== TensorDaySectionDataset 测试脚本 ==="
echo "数据路径: $DATA_PATH"
echo ""

# 检查数据文件是否存在
if [[ ! -f "$DATA_PATH" ]]; then
    echo "❌ 错误: 数据文件不存在: $DATA_PATH"
    exit 1
fi

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 运行单卡测试
# if [[ "$RUN_SINGLE" == "true" ]]; then
#     echo "🚀 开始单卡测试..."
#     echo "----------------------------------------"
#     cd "$SCRIPT_DIR"
#     python test_dataset_single.py --data_path "$DATA_PATH"
    
#     if [[ $? -eq 0 ]]; then
#         echo "✅ 单卡测试通过"
#     else
#         echo "❌ 单卡测试失败"
#         exit 1
#     fi
#     echo ""
# fi

# 运行DDP测试
if [[ "$RUN_DDP" == "true" ]]; then
    echo "🚀 开始DDP测试..."
    echo "----------------------------------------"
    cd "$SCRIPT_DIR"
    
    # 检查是否有多个GPU
    if command -v nvidia-smi &> /dev/null; then
        GPU_COUNT=$(nvidia-smi --list-gpus | wc -l)
        echo "检测到 $GPU_COUNT 个GPU"
        
        if [[ $GPU_COUNT -gt 1 ]]; then
            echo "使用多GPU DDP测试..."
            torchrun --nproc_per_node=$GPU_COUNT test_dataset_ddp.py --data_path "$DATA_PATH"
        else    
            echo "使用单GPU DDP测试..."
            torchrun --nproc_per_node=1 test_dataset_ddp.py --data_path "$DATA_PATH"
        fi
    else
        echo "未检测到NVIDIA GPU，使用CPU DDP测试..."
        torchrun --nproc_per_node=1 test_dataset_ddp.py --data_path "$DATA_PATH"
    fi
    
    if [[ $? -eq 0 ]]; then
        echo "✅ DDP测试通过"
    else
        echo "❌ DDP测试失败"
        exit 1
    fi
    echo ""
fi

echo "🎉 所有测试完成！"
