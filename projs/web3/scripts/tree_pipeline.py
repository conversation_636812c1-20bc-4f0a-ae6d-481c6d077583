"""
Tree Pipeline - 树模型端到端训练

特点:
- 直接从X[F,D,T,N]构建表格样本，无需特征标准化
- 对y进行截面zscore处理（沿N轴），保留NaN
- 支持超大数据的memmap处理
- 使用Optuna进行超参优化
- 滚动训练和测试
- W&B记录实验
"""

import os, time, argparse, numpy as np
from dataclasses import dataclass
from loguru import logger
import sys

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../..'))

from MLlib.utils.rolling import rolling_splits_by_years
from MLlib.utils.metrics import rank_ic_focus
from MLlib.utils.flatten import flat_to_cube
from MLlib.models.lgbm_trainer import train_lgbm_one_fold, optuna_search_global, save_params_json
from MLlib.data.tree_dataset import TreeCubeDataset
from DLlib.train.wandb_tracker import WandbTracker
from DLlib.data.field_spec import FieldSpec  # 只为取 n_feat/n_label/n_weight
from DLlib.data.ddp_utils import set_global_seed


@dataclass
class TreeConfig:
    """树模型Pipeline配置"""
    # 实验名称
    exp_name: str = f"tree_{time.strftime('%m%d_%H%M%S')}"
    
    # 数据配置
    data_path: str = "/disk4/shared/intern/laiyc/forModel/alpha20.npy"
    
    # FieldSpec配置
    n_feat: int = 97
    n_label: int = 1 
    n_weight: int = 0

    # 滚动配置
    train_years: float = 3.0
    test_years: float = 1.0
    days_per_year: int = 240 
    inner_val_ratio: float = 0.2

    # Optuna搜参配置
    n_trials: int = 1 # 50
    n_sample_days: int = 300  # 全局随机抽样天数
    seed: int = 666
    force_optuna: bool = True  # 如果为True，强制进行Optuna搜索；否则使用预设的best_params

    # LGB基础参数（固定学习率等；搜索其余）
    base_params: dict = None
    # 预设的最优参数（默认使用，除非force_optuna=True）
    best_params: dict = None
    early_stopping_rounds: int = 30
    verbose_eval: int = 1

    # 关注的分钟索引
    T_focus = [30, 60, 90, 120, 150, 180, 210]

    # 输出目录
    ckpt_root: str = f"projs/stock1m/checkpoints_tree/{exp_name}"
    work_root: str = "projs/stock1m/work_tree"  # memmap/中间文件, 暂时没用

    # W&B配置
    wandb_project: str = "tslib"
    wandb_group: str = "stock1m_tree"
    wandb_mode: str = "offline"
    wandb_dir: str = f"projs/stock1m/logs/wandb/{exp_name}"
    wandb_tags: list = None

    def __post_init__(self):
        if self.base_params is None:
            self.base_params = {
                "objective": "regression",
                "metric": None,            # 用自定义 IC 早停
                "boosting_type": "gbdt",
                "learning_rate": 0.2,     # 固定学习率，每个任务proj手调
                "num_iterations": 1000,
                "device": "cpu",           # 使用 CPU 多核
                "n_jobs": os.cpu_count() or 88,
                "verbose": -1,
                "seed": self.seed,         # 设置LightGBM随机种子
                "feature_fraction_seed": self.seed,  # 特征采样种子
                "bagging_seed": self.seed,           # bagging种子
                "data_random_seed": self.seed,       # 数据随机种子
            }

        if self.best_params is None:
            # 预设的最优参数（基于历史Optuna搜索结果）
            self.best_params = dict(self.base_params)
            self.best_params.update({
                #"lambda_l1": 10,
                #"lambda_l2": 10,
                #"num_leaves": 512,
                #"max_depth": 8,
                #"feature_fraction": 0.8,
                #"bagging_fraction": 0.8,
                #"bagging_freq": 5,
            })

        if self.wandb_tags is None:
            self.wandb_tags = ["Tree", "LightGBM", "IC-focus", "CPU"]



def run(cfg: TreeConfig):
    """运行树模型Pipeline"""

    # 设置全局随机种子
    set_global_seed(cfg.seed)

    # 1) 读取超大 X：mmap模式，不占内存
    logger.info(f"=== Tree Pipeline ===")
    logger.info(f"正在加载特征数据: {cfg.data_path}")
    X = np.load(cfg.data_path, mmap_mode="r")   # [F,D,T,N]，按需访问
    F, D, T, N = X.shape
    
    spec = FieldSpec(n_feat=cfg.n_feat, n_label=cfg.n_label, n_weight=cfg.n_weight)
    assert spec.n_feat + spec.n_weight + spec.n_label == F, f"特征维度不匹配: {spec.n_feat}+{spec.n_weight}+{spec.n_label} != {F}"

    logger.info(f"最终数据形状: F={F}, D={D}, T={T}, N={N}")
    logger.info(f"特征配置: n_feat={cfg.n_feat}, n_label={cfg.n_label}, n_weight={cfg.n_weight}")

    # 2) 生成滚动切分
    folds = rolling_splits_by_years(
        D=D, 
        train_years=cfg.train_years, 
        test_years=cfg.test_years,
        days_per_year=cfg.days_per_year, 
        inner_val_ratio=cfg.inner_val_ratio,
    )
    logger.info(f"滚动配置: {cfg.train_years}年训练 + {cfg.test_years}年测试")
    logger.info(f"总计 {len(folds)} 个折")

    # 3) 初始化W&B跟踪器（单进程）
    tracker = WandbTracker(
        project=cfg.wandb_project,
        run_name=f"{cfg.exp_name}-LGBM-{cfg.train_years}x{cfg.test_years}",
        group=cfg.wandb_group,
        mode=cfg.wandb_mode,
        dir_=cfg.wandb_dir,
        tags=cfg.wandb_tags,
        config={
            "pipeline": "Tree",
            "model": "LightGBM",
            "base_params": cfg.base_params,
            "T_focus": cfg.T_focus,
            "optuna": {
                "n_trials": cfg.n_trials,
                "seed": cfg.seed,
            },
            "rolling": f"{cfg.train_years}x{cfg.test_years} years, val_ratio={cfg.inner_val_ratio}",
            "data_shape": [F, D, T, N],
            "num_folds": len(folds),
        },
    )

    # 创建输出目录
    os.makedirs(cfg.ckpt_root, exist_ok=True)

    # 4) 获取最优参数：要么使用预设参数，要么进行Optuna搜索
    if not cfg.force_optuna:
        # 使用预设的最优参数，跳过Optuna搜索
        logger.info("[Phase 1] 使用预设最优参数，跳过Optuna搜索...")
        best_params = cfg.best_params.copy()

        # 保存参数到当前实验目录（方便追踪）
        params_path = os.path.join(cfg.ckpt_root, "best_params.json")
        save_params_json(best_params, params_path)

        # 记录到W&B
        tracker.log_metrics({"optuna/n_trials": 0, "optuna/skipped": True, "optuna/use_preset": True})
        # tracker.save_artifact(params_path, type_="config")

        logger.info(f"使用预设参数完成，已保存到: {params_path}")
        logger.info(f"预设参数: {best_params}")
    else:
        # 进行Optuna超参搜索
        logger.info("[Phase 1] 强制开启Optuna全局搜索...")
        logger.info(f"随机抽样 {cfg.n_sample_days} 天进行超参搜索")

        # 构建全局数据集
        logger.info(f"正在构建全局数据集...")
        dataset_global = TreeCubeDataset(
            X,
            n_feat=spec.n_feat,
            n_weight=spec.n_weight,
            n_label=spec.n_label
        )

        # 全局随机抽样搜参
        logger.info(f"开始全局 Optuna 搜参...")
        best_params = optuna_search_global(
            dataset_global,
            n_sample_days=cfg.n_sample_days,
            inner_val_ratio=cfg.inner_val_ratio,
            base_params=cfg.base_params,
            n_trials=cfg.n_trials,
            seed=cfg.seed,
            T_focus=cfg.T_focus,
        )

        # 保存最优参数
        params_path = os.path.join(cfg.ckpt_root, "best_params.json")
        save_params_json(best_params, params_path)

        # 记录到W&B
        tracker.log_metrics({"optuna/n_trials": cfg.n_trials, "optuna/skipped": False, "optuna/use_preset": False})
        # tracker.save_artifact(params_path, type_="config")

        logger.info(f"超参搜索完成，最优参数已保存到: {params_path}")

    # 5) 固定最优参数，滚动训练所有折并测试
    logger.info("[Phase 2] 开始滚动训练...")
    all_valid_ic, all_test_ic = [], []
    
    for fid, (idx_tr, idx_va, idx_te) in enumerate(folds):
        logger.info(f"[Fold {fid}] 开始训练")
        logger.info(f"  训练: {len(idx_tr)}天, 验证: {len(idx_va)}天, 测试: {len(idx_te)}天")
        
        fold_dir = os.path.join(cfg.ckpt_root, f"fold{fid:02d}")
        os.makedirs(fold_dir, exist_ok=True)

        logger.info(f"[Fold {fid}] 正在构建TreeCubeDataset...")
        # 构建训练、验证、测试数据
        ds = TreeCubeDataset(
            X,
            n_feat=spec.n_feat,
            n_weight=spec.n_weight,
            n_label=spec.n_label
        )

        logger.info(f"[Fold {fid}] 正在切片和扁平数据...")
        tr, va, te = ds.split_and_flatten(
            idx_tr=idx_tr,
            idx_va=idx_va,
            idx_te=idx_te,
            drop_nan_y=True
        )

        logger.info(f"  样本数 - 训练: {tr['X'].shape[0]}, 验证: {va['X'].shape[0]}, 测试: {te['X'].shape[0]}")

        # 训练模型
        logger.info(f"[Fold {fid}] 正在训练模型...")
        booster, va_metrics = train_lgbm_one_fold(
            tr, va, 
            lgb_params=best_params,
            early_stopping_rounds=cfg.early_stopping_rounds, 
            verbose_eval=cfg.verbose_eval,
            T_focus=cfg.T_focus
        )
        
        va_ic = va_metrics["valid/ic_focus"]
        all_valid_ic.append(va_ic)
        
        # 测试
        logger.info(f"[Fold {fid}] 正在预测测试集...")
        te_pred = booster.predict(te["X"], num_iteration=booster.best_iteration)
        te_ic = rank_ic_focus(te_pred, te["y"], te["d"], te["t"], te["n"], T_focus=cfg.T_focus)
        all_test_ic.append(te_ic)
        
        # 记录单个fold结果 - 使用简化方式
        tracker.log_metrics({f"fold_{fid}": te_ic})

        # 保存模型和预测结果
        model_path = os.path.join(fold_dir, "lgbm.txt")
        booster.save_model(model_path)

        # 将扁平预测和标签重构为立方体格式 [D_test, T, N]
        te_pred_cube = flat_to_cube(
            te_pred, te["d"], te["t"], te["n"],
            idx_te, ds.T, ds.N
        )
        te_label_cube = flat_to_cube(
            te["y"], te["d"], te["t"], te["n"],
            idx_te, ds.T, ds.N
        )

        pred_cube_path = os.path.join(fold_dir, "test_pred_cube.npy")
        label_cube_path = os.path.join(fold_dir, "test_label_cube.npy")

        np.save(pred_cube_path, te_pred_cube.astype(np.float32))
        #np.save(label_cube_path, te_label_cube.astype(np.float32))

        # 保存到W&B
        # tracker.save_artifact(model_path, type_="model")
        # tracker.save_artifact(pred_cube_path, type_="predictions")
        # tracker.save_artifact(label_cube_path, type_="labels")
        
        logger.info(f"[Fold {fid}] 完成 - Valid IC: {va_ic:.6f}, Test IC: {te_ic:.6f}")

        # 资源清理
        del booster, tr, va, te, ds, te_pred_cube, te_label_cube
        import gc, psutil
        gc.collect()
        logger.info(f"[Fold {fid}] 资源清理完成，当前RSS={psutil.Process().memory_info().rss/1024**3:.2f} GB")

    # 6) 汇总结果
    if len(all_test_ic):
        cv_mean = float(np.mean(all_test_ic))
        cv_std = float(np.std(all_test_ic))
        
        tracker.log_metrics({
            "cv_mean": cv_mean,
            "cv_std": cv_std,
            "cv_folds": len(folds)
        })
        
        logger.info("=== 最终结果汇总 ===")
        logger.info(f"CV总结 - 平均测试IC: {cv_mean:.6f} ± {cv_std:.6f}")
        logger.info(f"总计 {len(folds)} 个折")

    # 结束实验
    tracker.finish()
    logger.info("Tree Pipeline 完成!")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="Tree Pipeline")
    parser.add_argument("--data_path", type=str,
                       default="projs/stock1m/data/OHLCVA_Vwap_cube.npy",
                       help="数据文件路径")
    parser.add_argument("--exp_name", type=str,
                       default=f"tree_{time.strftime('%m%d_%H%M%S')}",
                       help="实验名称")
    parser.add_argument("--n_trials", type=int, default=50,
                       help="Optuna搜参试验次数")
    parser.add_argument("--wandb_mode", type=str, default="online",
                       choices=["online", "offline"],
                       help="W&B模式")
    parser.add_argument("--force_optuna", action="store_true",
                       help="强制进行Optuna搜索，否则使用预设最优参数")
    args = parser.parse_args()

    # 创建配置
    cfg = TreeConfig(
        data_path=args.data_path,
        exp_name=args.exp_name,
        n_trials=args.n_trials,
        wandb_mode=args.wandb_mode,
        force_optuna=args.force_optuna,
    )
    
    # 运行pipeline
    run(cfg)


if __name__ == "__main__":
    main()