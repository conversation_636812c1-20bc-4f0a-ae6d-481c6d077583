"""
训练指标和损失函数

提供RMSE、IC等常用指标，支持DDP分布式训练。
"""

from typing import Optional, Sequence
import torch
import torch.distributed as dist


def ddp_mean_scalar(x: torch.Tensor) -> torch.Tensor:
    """
    DDP环境下对标量进行平均，自动过滤nan值
    
    参数:
        x: 标量张量
    
    返回:
        平均后的标量张量，如果全是nan则返回nan
    """
    # if not torch.isfinite(x):
    #     return x
    
    if dist.is_available() and dist.is_initialized():
        y = x.detach().clone()
        # 统计有效值数量
        valid = torch.tensor(1.0 if torch.isfinite(y) else 0.0, device=y.device)
        dist.all_reduce(valid, op=dist.ReduceOp.SUM)
        if valid == 0:
            return torch.tensor(float('nan'), device=y.device)
            
        # 对有效值求和取平均
        dist.all_reduce(y, op=dist.ReduceOp.SUM)
        y /= valid
        return y
    return x


def rmse(pred: torch.Tensor, target: torch.Tensor, weight: Optional[torch.Tensor] = None, eps: float = 1e-12) -> torch.Tensor:
    """
    计算加权RMSE

    Formula:
        rmse = sqrt( (1/sum(w)) * sum(w * (pred - target)^2) )
    
    参数:
        pred: 预测值，形状 [..., C]
        target: 真实值，形状与pred相同
        weight: 权重，可广播到pred，可选
        eps: 数值稳定性参数
    
    返回:
        RMSE值
    """
    # 创建nan掩码
    mask = torch.isfinite(pred) & torch.isfinite(target)
    pred = pred[mask]
    target = target[mask]
    if weight is not None:
        weight = weight[mask]
    else:
        weight = torch.ones_like(pred)
    
    err2 = (pred - target) ** 2
    if weight is not None:
        err2 = err2 * weight
        denom = torch.clamp(weight.sum(), min=eps) if weight.numel() == err2.numel() else err2.numel()
        return torch.sqrt(err2.sum() / denom + eps)
    return torch.sqrt(err2.mean() + eps)

import torch
from typing import Optional, Sequence

@torch.no_grad()
def ic_cs(
    pred: torch.Tensor,
    target: torch.Tensor,
    weight: Optional[torch.Tensor] = None,   # 预留，不参与当前计算
    *,
    T_idx: Optional[Sequence[int]] = None,   # 仅用于 [N,T,1]；4D 情况固定内部 T_focus
    mode: str = "spearman_approx",                   # "pearson" | "spearman_approx"
    eps: float = 1e-12,
) -> torch.Tensor:
    """
    计算截面 IC（Information Coefficient）

    支持三种输入形状：
      A) 非回看: pred/target ∈ [N, T, 1]
         - 取 T_idx 中每个 t，做 corr_N(pred[:,t,0], target[:,t,0])
         - 过滤策略：仅保留在所有被使用 t 上均为有限数的股票（N_valid），再对每个 t 算 corr 并对 t 平均
      B) 回看:   pred/target ∈ [N, 1] 或 [N]
         - 直接对 N 维做 corr，过滤掉任何 NaN 位置（pred/target 同为有限才纳入）
      C) 4D:     pred/target ∈ [D, N, T, 1]
         - 固定使用 T_focus = (30,60,90,120,150,180,210,230)
         - 对每个 day，按 A 的规则算出 daily IC，然后对 D 平均

    返回:
      标量 torch.Tensor（不可导）
    """
    assert pred.shape == target.shape, f"pred/label 形状不一致: {pred.shape} vs {target.shape}"
    x = pred.detach().float()
    y = target.detach().float()

    def _spearman_rank(v: torch.Tensor) -> torch.Tensor:
        # v: [..., N]（最后一维是 N），这里我们会按 N 维做 rank
        # 但在本函数内部我们会构造成 [N,k] 形状后沿 dim=0 排序
        # 所以这里只在 1D/2D 情况下用到
        order = torch.argsort(v, dim=0)
        ranks = torch.argsort(order, dim=0).float()
        return ranks

    def _pearson_cols(X: torch.Tensor, Y: torch.Tensor) -> torch.Tensor:
        """
        X,Y: [N_valid, K] （K 列，每列一个 t 或多个 t）
        返回: [K]（每列一个相关）
        """
        Xc = X - X.mean(dim=0, keepdim=True)
        Yc = Y - Y.mean(dim=0, keepdim=True)
        num = (Xc * Yc).sum(dim=0)
        den = torch.sqrt(Xc.square().sum(dim=0) * Yc.square().sum(dim=0) + eps)
        corr = num / (den + eps)
        return corr

    def _spearman_cols(X: torch.Tensor, Y: torch.Tensor) -> torch.Tensor:
        # 逐列的近似 Spearman：对 N 维 rank 后再 Pearson
        # X,Y: [N_valid, K]
        # 用 torch 排序两次得到秩
        # 先转到 CPU 保守些（如果 N 很大，CUDA 也可以直接跑）
        # 这里不强制转 CPU，保持 device 一致
        Xr = torch.argsort(torch.argsort(X, dim=0), dim=0).float()
        Yr = torch.argsort(torch.argsort(Y, dim=0), dim=0).float()
        return _pearson_cols(Xr, Yr)

    def _corr_cols(X: torch.Tensor, Y: torch.Tensor) -> torch.Tensor:
        return _pearson_cols(X, Y) if mode == "pearson" else _spearman_cols(X, Y)

    # ------- 分形状处理 -------
    # 查看是否是[?, 1, 1]:
    if x.shape[1] == 1 and x.shape[2] == 1:
        x = x.squeeze(1)
        y = y.squeeze(1)
    
    if x.dim() == 4:
        # [D,N,T,1]：按日算，再对日平均；T 刻度固定为 8 个关心点
        D, N, T, C = x.shape
        assert C == 1, f"期望最后一维=1，得到 {C}"
        T_focus = (30, 60, 90, 120, 150, 180, 210)
        T_focus = [t for t in T_focus if 0 <= t < T]
        if len(T_focus) == 0:
            print("warning: len(T_focus) == 0")
            return torch.tensor(float(0.0), device=x.device)

        ics = []
        for d in range(D):
            # 取被使用的列：得到 [N, k]
            Xdt = x[d, :, T_focus, 0]    # [N, k]
            Ydt = y[d, :, T_focus, 0]    # [N, k]
            # 过滤：仅保留在所有被使用 t 上 x/y 均为有限的股票
            fin_xy = torch.isfinite(Xdt) & torch.isfinite(Ydt)   # [N,k]
            mask_n = fin_xy.all(dim=1)                           # [N]
            if mask_n.sum().item() < 3:                          # 样本太少，跳过或记 NaN
                continue
            Xv = Xdt[mask_n, :]                                  # [N_valid,k]
            Yv = Ydt[mask_n, :]
            cor = _corr_cols(Xv, Yv).mean()                      # k 列平均
            if torch.isfinite(cor):
                ics.append(cor)
        if len(ics) == 0:
            print("warning: len(ics) == 0")
            return torch.tensor(float("nan"), device=x.device)
        return torch.stack(ics).mean()

    elif x.dim() == 3:
        # [N,T,1]
        N, T, C = x.shape
        assert C == 1, f"期望最后一维=1，得到 {C}"
        if T_idx is None:
            T_idx = (30, 60, 90, 120, 150, 180, 210)  # 你关心的 8 个刻度
        T_used = [t for t in T_idx if 0 <= t < T]
        if len(T_used) == 0:
            print("warning: len(T_used) == 0")
            return torch.tensor(float(0.0), device=x.device)

        X = x[:, T_used, 0]           # [N,k]
        Y = y[:, T_used, 0]           # [N,k]

        fin_xy = torch.isfinite(X) & torch.isfinite(Y)  # [N,k]
        mask_n = fin_xy.all(dim=1)                      # [N] —— 必须在所有被用的 t 上都有限
        if mask_n.sum().item() < 3:
            print(f"Warning: mask_num.sum = {mask_n.sum().item()} < 3")
            return torch.tensor(float(0.0), device=x.device)

        Xv = X[mask_n, :]                                # [N_valid,k]
        Yv = Y[mask_n, :]
        return _corr_cols(Xv, Yv).mean()                 # k 列平均

    elif x.dim() == 2:
        # [N,1] 或 [N,k]（这里按 [N,1] 使用）
        if x.shape[1] != 1:
            raise ValueError(f"期望 [N,1]，但得到 {x.shape}")
        xv = x[:, 0]
        yv = y[:, 0]
        mask = torch.isfinite(xv) & torch.isfinite(yv)
        xv = xv[mask]
        yv = yv[mask]
        if xv.numel() < 3:
            print(f"Warning: xv.numel = {xv.numel()} < 3")
            return torch.tensor(float(0.0), device=x.device)
        if mode == "spearman_approx":
            xv = torch.argsort(torch.argsort(xv))
            yv = torch.argsort(torch.argsort(yv))
            xv = xv.float(); yv = yv.float()
        xv = xv - xv.mean()
        yv = yv - yv.mean()
        num = (xv * yv).sum()
        den = torch.sqrt(xv.square().sum() * yv.square().sum() + eps)
        return num / (den + eps)

    else:
        raise ValueError(f"不支持的形状: {x.shape}")


def weighted_spearmanr(y_true, y_pred):
    import numpy as np
    import pandas as pd
    """
    Calculate the weighted Spearman correlation coefficient according to the formula in the appendix:
    1) Rank y_true and y_pred in descending order (rank=1 means the maximum value)
    2) Normalize the rank indices to [-1, 1], then square to obtain the weight w_i
    3) Calculate the correlation coefficient using the weighted Pearson formula
    """

    device = y_true.device
    # detach from torch device and reshape 
    y_true = y_true.detach().cpu().numpy().reshape(-1)

    y_pred = y_pred.detach().cpu().numpy().reshape(-1)
    # number of samples
    n = len(y_true)
    # rank the true values in descending order (average method for handling ties)
    r_true = pd.Series(y_true).rank(ascending=False, method='average')
    # rank the predicted values in descending order (average method for handling ties)
    r_pred = pd.Series(y_pred).rank(ascending=False, method='average')
    
    # normalize the index i = rank - 1, mapped to [-1, 1]
    x = 2 * (r_true - 1) / (n - 1) - 1
    # weight w_i (the weight factor for each sample)
    w = x ** 2  
    
    # weighted mean
    w_sum = w.sum()
    mu_true = (w * r_true).sum() / w_sum
    mu_pred = (w * r_pred).sum() / w_sum
    
    # calculate the weighted covariance
    cov = (w * (r_true - mu_true) * (r_pred - mu_pred)).sum()
    # calculate the weighted variance of the true value rankings
    var_true = (w * (r_true - mu_true)**2).sum()
    # calculate the weighted variance of the predicted value rankings
    var_pred = (w * (r_pred - mu_pred)**2).sum()
    
    # return the weighted Spearman correlation coefficient
    ic = (cov / np.sqrt(var_true * var_pred))
    return torch.tensor(ic, device=device)