import torch, types

SUSPECT_SET = {
    "LayerNorm", "RMSNorm", "BatchNorm1d", "BatchNorm2d",
    "Softmax", "LogSoftmax", "SiLU", "GELU", "ReLU",
}

def _make_nan_hook(mod_name: str):
    def hook(module, inputs, output):
        def has_bad(t):
            return torch.is_tensor(t) and (~torch.isfinite(t)).any().item()
        # 检查输出（优先看输出，最容易暴露问题）
        outs = output if isinstance(output, (tuple, list)) else (output,)
        for t in outs:
            if has_bad(t):
                # 打点信息：层名、dtype、数值范围等
                with torch.no_grad():
                    msg = (f"[NaNGuard] OUTPUT NaN/Inf at <{mod_name}> "
                           f"dtype={t.dtype} ")
                           #f"min={torch.nanmin(t).item()} max={torch.nanmax(t).item()}")
                raise RuntimeError(msg)

        # 如需，也可检查输入
        ins = []
        for inp in inputs:
            if isinstance(inp, (tuple, list)):
                ins += list(inp)
            else:
                ins.append(inp)
        for t in ins:
            if has_bad(t):
                raise RuntimeError(f"[NaNGuard] INPUT NaN/Inf at <{mod_name}>")
    return hook

def _is_leaf_like(m: torch.nn.Module) -> bool:
    # 没有子模块的算子，或带有权重/偏置的层，或在敏感集合中
    return (len(list(m.children())) == 0) or \
           any(hasattr(m, a) for a in ("weight", "bias")) or \
           (m.__class__.__name__ in SUSPECT_SET)

def install_nan_hooks(model: torch.nn.Module):
    handles = []
    for name, m in model.named_modules():
        if name == "" or not _is_leaf_like(m):
            continue
        h = m.register_forward_hook(_make_nan_hook(name))
        handles.append(h)
    return handles

def remove_hooks(handles):
    for h in handles:
        h.remove()
