import math
import torch
import torch.nn as nn
import torch.nn.functional as Fnn

## ---------------- Mo<PERSON>le Block -------------------------------

# -------- 基础：分钟级位置编码（对 T 维；对所有股票广播） --------
class MinutePositionalEncoding(nn.Module):
    def __init__(self, d_model: int, max_len: int = 512):
        super().__init__()
        pe = torch.zeros(max_len, d_model)
        pos = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        div = torch.exp(torch.arange(0, d_model, 2).float() * (-math.log(10000.0) / d_model))
        pe[:, 0::2] = torch.sin(pos * div)
        pe[:, 1::2] = torch.cos(pos * div)
        self.register_buffer("pe", pe)  # [max_len, d_model]

    def forward(self, x):  # x: [N, T, d]
        T = x.size(1)
        return x + self.pe[:T, :].unsqueeze(0)  # 广播到 [1, T, d]


# -------- 截面线性注意力（Performer 风格），按时点并行，无 NxN --------
class CrossSectionalLinearAttention(nn.Module):
    """
    输入/输出：X ∈ [N, T, d_model]，逐时刻 t 做 N 轴注意力：
      Y_t = softmax(Q_t K_t^T) V_t  ≈  (phi(Q_t) @ (phi(K_t)^T V_t)) / (phi(Q_t) @ (phi(K_t)^T 1))
    做多头，head 维度拼接后做 FFN + 残差 + LN。
    """
    def __init__(self, d_model: int, nhead: int = 4, dropout: float = 0.1):
        super().__init__()
        assert d_model % nhead == 0
        self.d_model = d_model
        self.nhead = nhead
        self.d_head = d_model // nhead
        self.scale = 1.0 / (self.d_head ** 0.5)

        self.q_proj = nn.Linear(d_model, d_model, bias=False)
        self.k_proj = nn.Linear(d_model, d_model, bias=False)
        self.v_proj = nn.Linear(d_model, d_model, bias=False)
        self.out_proj = nn.Linear(d_model, d_model, bias=False)

        self.dropout = nn.Dropout(dropout)
        self.norm = nn.LayerNorm(d_model)
        self.ffn = nn.Sequential(
            nn.Linear(d_model, 4 * d_model),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(4 * d_model, d_model),
            nn.Dropout(dropout),
        )

        self.ffn_norm = nn.LayerNorm(d_model)

    @staticmethod
    def _phi(x):
        # 正核映射，确保正值近似 softmax 的归一性；ELU+1 是常见简洁选择
        return Fnn.elu(x, alpha=1.0) + 1.0

    def forward(self, x):  # x: [N, T, d]
        N, T, D = x.shape
        residual = x
        x = self.norm(x)

        # 先转成 [T, N, d]，便于“每个时刻”的并行计算
        x_tnd = x.transpose(0, 1)  # [T, N, d]

        Q = self.q_proj(x_tnd)  # [T, N, d]
        K = self.k_proj(x_tnd)
        V = self.v_proj(x_tnd)

        # 多头拆分：-> [T, N, nH, dH] 再合并到 [T, nH, N, dH]
        def split_heads(tnd):
            return tnd.view(T, N, self.nhead, self.d_head).permute(0, 2, 1, 3)

        Qh = split_heads(Q) * self.scale # [T, H, N, dH]
        Kh = split_heads(K) * self.scale
        Vh = split_heads(V)

        # 核映射
        phiQ = self._phi(Qh)  # [T, H, N, dH]
        phiK = self._phi(Kh)  # [T, H, N, dH]

        # 计算 (phi(K)^T V) 与 (phi(K)^T 1)，逐时刻批量化，无 NxN：
        #   KV: [T, H, dH, dH]，  K1: [T, H, dH]
        KV = torch.einsum('thnd,thne->thde', phiK, Vh)        # sum over N
        K1 = torch.einsum('thnd->thd',       phiK)            # sum over N

        # 输出：Y = (phiQ @ KV) / (phiQ @ K1)[..., None]
        num = torch.einsum('thnd,thde->thne', phiQ, KV)       # [T, H, N, dH]
        den = torch.einsum('thnd,thd->thn',  phiQ, K1).unsqueeze(-1)  # [T, H, N, 1]
        eps = 1e-6
        Yh = num / (den + eps)

        # 合并多头回到 [T, N, d] -> [N, T, d]
        Y = Yh.permute(0, 2, 1, 3).contiguous().view(T, N, D)
        Y = self.out_proj(Y)
        Y = self.dropout(Y)
        Y = Y + x_tnd  # 残差
        Y = Y.transpose(0, 1)  # [N, T, d]

        # FFN + 残差
        Z = self.ffn(self.ffn_norm(Y)) + Y
        return Z  # [N, T, d]

# -------- 因果时序注意力（对 T ） --------
class TemporalCausalAttention(nn.Module):
    """
    因果多头时序注意力（沿 T 轴; 每支股票独立做; 不涉及 N×N）
    输入:  x [N, T, d_in]
    输出:  y [N, T, d_model]
    """
    def __init__(self, d_in: int, d_model: int, nhead: int = 4, dropout: float = 0.1, use_sdpa: bool = True):
        super().__init__()
        assert d_model % nhead == 0
        self.nhead = nhead
        self.d_head = d_model // nhead
        self.use_sdpa = use_sdpa

        self.q_proj = nn.Linear(d_in,    d_model, bias=False)
        self.k_proj = nn.Linear(d_in,    d_model, bias=False)
        self.v_proj = nn.Linear(d_in,    d_model, bias=False)
        self.out    = nn.Linear(d_model, d_model, bias=False)

        self.dropout = nn.Dropout(dropout)
        #self.norm1 = nn.LayerNorm(d_model)
        self.ffn   = nn.Sequential(
            nn.Linear(d_model, 4*d_model),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(4*d_model, d_model),
            nn.Dropout(dropout),
        )
        #self.norm2 = nn.LayerNorm(d_model)

    def forward(self, x):  # x: [N, T, d_in]
        N, T, _ = x.shape
        Q = self.q_proj(x)  # [N, T, d_m]
        K = self.k_proj(x)
        V = self.v_proj(x)

        # [N, T, H, dH] -> [N, H, T, dH]
        def split(t):
            return t.view(N, T, self.nhead, self.d_head).permute(0, 2, 1, 3).contiguous()

        qh, kh, vh = split(Q), split(K), split(V)

        if self.use_sdpa and hasattr(Fnn, "scaled_dot_product_attention"):
            # PyTorch 2.x 高效注意力，原生因果掩码
            attn_out = Fnn.scaled_dot_product_attention(
                qh, kh, vh,
                attn_mask=None,
                dropout_p=self.dropout.p if self.training else 0.0,
                is_causal=True
            )  # [N, H, T, dH]
        else:
            # 手工因果掩码
            scale = 1.0 / math.sqrt(self.d_head)
            scores = torch.matmul(qh, kh.transpose(-2, -1)) * scale  # [N,H,T,T]
            causal = torch.ones(T, T, device=scores.device, dtype=torch.bool).triu(1)
            scores.masked_fill_(causal, float('-inf'))
            attn = torch.softmax(scores, dim=-1)
            attn = self.dropout(attn)
            attn_out = torch.matmul(attn, vh)  # [N,H,T,dH]

        # 合并头 -> [N, T, d_model]
        y = attn_out.permute(0, 2, 1, 3).contiguous().view(N, T, -1)
        y = self.out(y)
        y = self.dropout(y)

        # 残差 + FFN
        #y = self.norm1(y)
        y = y + self.ffn(y)
        #y = self.norm2(y)
        return y  # [N, T, d_model]


## ---------------- Model -------------------------------

# 1. GRU_SAttn
# 2. TAttnOnly
# 3. MASTER_TAttn_SAttn
# 4. MASTER_SAtten_TAttn
# 5. SAttn_GRU

class GRU_SAttn(nn.Module):
    """
    输入:  X ∈ [N, T, F]
    输出:  Y ∈ [N, T, 1]  （逐时刻预测）
    组件:
      - 特征投影 + 分钟位置编码（对 T）
      - 单向 GRU（时间因果建模）
      - 逐时刻截面“线性注意力”（不构造 NxN）
      - 线性头
    """
    def __init__(
        self,
        d_feat: int,        # 输入特征维 F
        d_gru_model: int = 128, 
        d_atten_model: int = 48,
        s_nhead: int = 4,
        dropout: float = 0.1,
        # max_T: int = 512,   # 分钟上限（默认 > 241）
        gru_layers: int = 1, 
        demean: bool = True
    ):
        super().__init__()

        # self.pos_enc   = MinutePositionalEncoding(d_atten_model, max_len=max_T)

        # 时间因果：单向 GRU（batch_first=True：把 N 当 batch）
        self.gru = nn.GRU(
            input_size=d_feat,
            hidden_size=d_gru_model,
            num_layers=gru_layers,
            batch_first=True,
            bidirectional=False
        )

        self.feat_proj = nn.Linear(d_gru_model, d_atten_model, bias=False)

        # 截面注意力（线性近似，避免 NxN）
        self.s_attn = CrossSectionalLinearAttention(d_model=d_atten_model, nhead=s_nhead, dropout=dropout)

        # 输出头（逐时刻）
        self.head = nn.Sequential(
            # nn.LayerNorm(d_model),
            nn.Linear(d_atten_model, 1)
        )

        # 简单初始化
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight)
                if getattr(m, "bias", None) is not None:
                    nn.init.zeros_(m.bias)

        self.demean = demean

    @torch.no_grad()
    def _check_no_leakage(self, x):
        """
        自检：把后半段打乱，前半段输出必须一致。
        返回 (ok: bool, max_diff: float)
        """
        self.eval()
        x1 = x.clone()
        y1 = self.forward(x1).detach()

        x2 = x.clone()
        T = x2.size(1)
        tail = torch.rand_like(x2[:, T//2:, :])
        x2[:, T//2:, :] = tail
        y2 = self.forward(x2).detach()

        diff = (y1[:, :T//2, :] - y2[:, :T//2, :]).abs().max().item()
        return (diff == 0.0, diff)

    def forward(self, x):  # x: [N, T, F]
                         # [N, T, d]

        # 时间因果：单向 GRU
        # 这里把 N 当 batch，天然不会构造 [N, T, T]
        h, _ = self.gru(x)                    # [N, T, d]
        h = self.feat_proj(h)

        # 逐时刻截面注意力（线性近似），只用 X[:, t, :]，不看未来时刻
        h = self.s_attn(h)                    # [N, T, d]

        # 逐时刻线性头
        y = self.head(h)                      # [N, T, 1]

        # demean
        if self.demean:
            y = y - y.mean(dim=0, keepdim=True)
        return y


class TAttnOnly(nn.Module):
    """
    纯因果时序注意力 -> 线性头
    输入:  [N, T, F]    输出: [N, T, 1]
    """
    def __init__(self, d_feat: int, d_t_model: int = 128, t_nhead: int = 4, dropout: float = 0.1, use_sdpa: bool = True, demean: bool = True):
        super().__init__()
        self.t_attn = TemporalCausalAttention(d_in=d_feat, d_model=d_t_model, nhead=t_nhead, dropout=dropout, use_sdpa=use_sdpa)
        self.head   = nn.Linear(d_t_model, 1)
        self.demean = demean

    @torch.no_grad()
    def _check_no_leakage(self, x):
        self.eval()
        y1 = self.forward(x.clone()).detach()
        x2 = x.clone()
        T = x2.size(1)
        x2[:, T//2:, :] = torch.rand_like(x2[:, T//2:, :])
        y2 = self.forward(x2).detach()
        diff = (y1[:, :T//2, :] - y2[:, :T//2, :]).abs().max().item()
        return (diff == 0.0, diff)

    def forward(self, x):  # [N,T,F]
        h = self.t_attn(x)         # [N,T,d_t]
        y = self.head(h)           # [N,T,1]
        if self.demean:
            y = y - y.mean(dim=0, keepdim=True)  # 与你当前做法一致
        return y


class MASTER_TAttn_SAttn(nn.Module):
    """
    因果时序注意力 -> 维度降至 d_atten_model -> 截面线性注意力 -> 头
    输入: [N,T,F]  输出: [N,T,1]
    """
    def __init__(
        self,
        d_feat: int,
        d_t_model: int = 128,
        d_atten_model: int = 48,
        t_nhead: int = 4,
        s_nhead: int = 2,
        dropout: float = 0.1,
        use_sdpa: bool = True, 
        demean: bool = True
    ):
        super().__init__()
        self.t_attn   = TemporalCausalAttention(d_in=d_feat, d_model=d_t_model, nhead=t_nhead, dropout=dropout, use_sdpa=use_sdpa)
        self.feat_proj = nn.Linear(d_t_model, d_atten_model, bias=False)
        self.s_attn    = CrossSectionalLinearAttention(d_model=d_atten_model, nhead=s_nhead, dropout=dropout)
        self.head      = nn.Linear(d_atten_model, 1)

        # init
        nn.init.xavier_uniform_(self.feat_proj.weight)
        nn.init.xavier_uniform_(self.head.weight)
        if getattr(self.head, "bias", None) is not None:
            nn.init.zeros_(self.head.bias)

        self.demean = demean

    @torch.no_grad()
    def _check_no_leakage(self, x):
        self.eval()
        y1 = self.forward(x.clone()).detach()
        x2 = x.clone()
        T = x2.size(1)
        x2[:, T//2:, :] = torch.rand_like(x2[:, T//2:, :])
        y2 = self.forward(x2).detach()
        diff = (y1[:, :T//2, :] - y2[:, :T//2, :]).abs().max().item()
        return (diff == 0.0, diff)

    def forward(self, x):  # [N,T,F]
        h = self.t_attn(x)          # [N,T,d_t]
        h = self.feat_proj(h)       # [N,T,d_a]
        h = self.s_attn(h)          # [N,T,d_a]
        y = self.head(h)            # [N,T,1]
        if self.demean:
            y = y - y.mean(dim=0, keepdim=True)
        return y


class SAttn_GRU(nn.Module):
    def __init__(
        self,
        d_feat: int,        # 输入特征维 F
        d_atten_model: int = 48,
        d_gru_model: int = 128, 
        s_nhead: int = 4,
        dropout: float = 0.1,
        # max_T: int = 512,   # 分钟上限（默认 > 241）
        gru_layers: int = 1, 
        demean: bool = True
    ):
        super().__init__()

        # self.pos_enc   = MinutePositionalEncoding(d_atten_model, max_len=max_T)
        self.feat_proj = nn.Linear(d_feat, d_atten_model, bias=False)

        self.s_attn = CrossSectionalLinearAttention(d_model=d_atten_model, nhead=s_nhead, dropout=dropout)

        # 时间因果：单向 GRU（batch_first=True：把 N 当 batch）
        self.gru = nn.GRU(
            input_size=d_atten_model,
            hidden_size=d_gru_model,
            num_layers=gru_layers,
            batch_first=True,
            bidirectional=False
        )


        # 截面注意力（线性近似，避免 NxN）

        # 输出头（逐时刻）
        self.head = nn.Sequential(
            # nn.LayerNorm(d_model),
            nn.Linear(d_gru_model, 1)
        )

        # 简单初始化
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight)
                if getattr(m, "bias", None) is not None:
                    nn.init.zeros_(m.bias)

        self.demean = demean

    @torch.no_grad()
    def _check_no_leakage(self, x):
        """
        自检：把后半段打乱，前半段输出必须一致。
        返回 (ok: bool, max_diff: float)
        """
        self.eval()
        x1 = x.clone()
        y1 = self.forward(x1).detach()

        x2 = x.clone()
        T = x2.size(1)
        tail = torch.rand_like(x2[:, T//2:, :])
        x2[:, T//2:, :] = tail
        y2 = self.forward(x2).detach()

        diff = (y1[:, :T//2, :] - y2[:, :T//2, :]).abs().max().item()
        return (diff == 0.0, diff)

    def forward(self, x):  
                         
        x = self.feat_proj(x)
        x = self.s_attn(x)

        h, _ = self.gru(x)                   

        y = self.head(h)                     

        # demean
        if self.demean:
            y = y - y.mean(dim=0, keepdim=True)
        return y


if __name__ == "__main__":
    import torch
    import torch.nn.functional as Fnn
    import contextlib

    # ===== No-leakage Test ======
    torch.manual_seed(0)

    N, T, Fdim = 400, 96, 304
    device = torch.device("cuda:0")

    model = GRU_SAttn(
         d_feat=Fdim, d_gru_model=512, d_atten_model=256, 
         s_nhead=4, dropout=0.1, gru_layers=3
     ).to(device)

    # model = TAttnOnly(
    #     d_feat=128, d_t_model=184, t_nhead=2, dropout=0.0, use_sdpa=True
    # ).to(device)


    # model = MASTER_TAttn_SAttn(
    #     d_feat=128, d_t_model=128, d_atten_model=64, t_nhead=4, s_nhead=2, dropout=0.0, use_sdpa=True
    # ).to(device)

    # model = SAttn_GRU(
    #     d_feat=Fdim, d_atten_model=128, d_gru_model=256, s_nhead=4, dropout=0.0, gru_layers=1
    # ).to(device)

    x = torch.randn(N, T, Fdim, device=device)

    # 前向
    with torch.no_grad():
        y = model(x)
    print("y:", y.shape, "on", y.device)

    # 无泄露自检（eval + 关掉dropout）
    ok, diff = model._check_no_leakage(x)
    print("No-leakage:", ok, "max_abs_diff_on_first_half:", diff)

    # # ===== 配置 ===== 显存查看

    model.train()

    device = torch.device("cuda:0")
    dtype_mode = "fp32"   # 选项: "fp32" | "fp16" | "bf16"
    with_optimizer = False  # 若想把优化器状态也计入内存，改为 True

    # ===== 帮助函数 =====
    def gib(x): return x / (1024**3)

    if dtype_mode == "fp16":
        autocast_dtype = torch.float16
    elif dtype_mode == "bf16":
        autocast_dtype = torch.bfloat16
    else:
        autocast_dtype = None

    amp_ctx = (torch.amp.autocast(dtype=autocast_dtype, device_type='cuda')
               if autocast_dtype is not None else contextlib.nullcontext())
    scaler = torch.amp.GradScaler(enabled=(dtype_mode == "fp16"))

    # ===== 模型 & 数据 =====
    torch.manual_seed(0)

    x = torch.randn(N, T, Fdim, device=device)
    target = torch.randn(N, T, 1, device=device)

    if with_optimizer:
        opt = torch.optim.AdamW(model.parameters(), lr=1e-3)

    torch.cuda.empty_cache()
    torch.cuda.reset_peak_memory_stats(device)
    torch.cuda.synchronize()

    start_alloc = torch.cuda.memory_allocated(device)
    start_res   = torch.cuda.memory_reserved(device)
    print(f"[Start] allocated={gib(start_alloc):.3f} GiB, reserved={gib(start_res):.3f} GiB")

    # ===== 前向 =====
    if with_optimizer:
        opt.zero_grad(set_to_none=True)
    else:
        model.zero_grad(set_to_none=True)

    torch.cuda.reset_peak_memory_stats(device)  # 单独统计前向峰值
    with amp_ctx:
        y = model(x)                  # [N, T, 1]
        loss = Fnn.mse_loss(y, target)

    torch.cuda.synchronize()
    fwd_alloc_peak = torch.cuda.max_memory_allocated(device)
    fwd_res_peak   = torch.cuda.max_memory_reserved(device)
    print(f"[FWD peak] allocated={gib(fwd_alloc_peak):.3f} GiB, reserved={gib(fwd_res_peak):.3f} GiB")

    # ===== 反向 =====
    torch.cuda.reset_peak_memory_stats(device)  # 单独统计反向新增峰值
    if dtype_mode == "fp16":
        scaler.scale(loss).backward()
        if with_optimizer:
            scaler.step(opt)
            scaler.update()
    else:
        loss.backward()
        if with_optimizer:
            opt.step()

    torch.cuda.synchronize()
    bwd_alloc_peak = torch.cuda.max_memory_allocated(device)
    bwd_res_peak   = torch.cuda.max_memory_reserved(device)
    print(f"[BWD peak] allocated={gib(bwd_alloc_peak):.3f} GiB, reserved={gib(bwd_res_peak):.3f} GiB")

    # ===== 总结 =====
    total_peak_alloc = max(fwd_alloc_peak, bwd_alloc_peak)
    total_peak_res   = max(fwd_res_peak,   bwd_res_peak)
    print(f"[TOTAL peak] allocated={gib(total_peak_alloc):.3f} GiB, reserved={gib(total_peak_res):.3f} GiB")

    # 可选：更详细的摘要
    print(torch.cuda.memory_summary(device=device, abbreviated=True))

