"""
序列模型

提供适用于时间序列预测的模型架构。
"""

import torch
import torch.nn as nn


class GRUSeq(nn.Module):
    """
    基于GRU的序列到序列模型
    
    适用于：
    - TensorDaySectionDataset: 输入 [N, T, F] -> 输出 [N, T, 1]
    - TensorPairDataset: 输入 [B, T, F] -> 输出 [B, T, 1]
    
    注意：使用单向GRU，避免未来信息泄露
    """
    
    def __init__(
        self, 
        input_dim: int, 
        hidden_dim: int = 256, 
        num_layers: int = 2,
        dropout: float = 0.1,
        output_dim: int = 1, 
        demean: bool = True
    ):
        """
        参数:
            input_dim: 输入特征维度
            hidden_dim: 隐藏层维度
            num_layers: GRU层数
            dropout: dropout比例
            output_dim: 输出维度
        """
        super().__init__()
        
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        self.num_layers = num_layers
        
        self.gru = nn.GRU(
            input_size=input_dim,
            hidden_size=hidden_dim,
            num_layers=num_layers,
            batch_first=True,
            bidirectional=False,  # 单向，避免未来信息泄露
            dropout=dropout if num_layers > 1 else 0
        )
        
        self.head = nn.Linear(hidden_dim, output_dim)
        self.dropout = nn.Dropout(dropout)

        self.demean = demean
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        参数:
            x: 输入张量，形状 [N, T, F] 或 [B, T, F]
        
        返回:
            输出张量，形状 [N, T, 1] 或 [B, T, 1]
        """
        # GRU前向传播
        gru_out, _ = self.gru(x)  # [N, T, hidden_dim]
        
        # 应用dropout
        gru_out = self.dropout(gru_out)
        
        # 线性层输出
        output = self.head(gru_out)  # [N, T, output_dim]

        if self.demean:
            output = output - output.mean(dim=0, keepdim=True)
        
        return output


class GRUPoint(nn.Module):
    """
    基于GRU的点预测模型
    
    适用于：
    - TensorMinuteLookbackWithinDayDataset: 输入 [N, L, F] -> 输出 [N, 1]
    - TensorMinuteLookbackAcrossDaysDataset: 输入 [N, L, F] -> 输出 [N, 1]
    
    使用回看窗口预测单个时间点
    """
    
    def __init__(
        self, 
        input_dim: int, 
        hidden_dim: int = 256, 
        num_layers: int = 2,
        dropout: float = 0.1,
        output_dim: int = 1, 
        demean: bool = True
    ):
        """
        参数:
            input_dim: 输入特征维度
            hidden_dim: 隐藏层维度
            num_layers: GRU层数
            dropout: dropout比例
            output_dim: 输出维度
        """
        super().__init__()
        
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        self.num_layers = num_layers
        
        self.gru = nn.GRU(
            input_size=input_dim,
            hidden_size=hidden_dim,
            num_layers=num_layers,
            batch_first=True,
            bidirectional=False,
            dropout=dropout if num_layers > 1 else 0
        )
        
        self.head = nn.Linear(hidden_dim, output_dim)
        self.dropout = nn.Dropout(dropout)

        self.demean = demean
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        参数:
            x: 输入张量，形状 [N, L, F]
        
        返回:
            输出张量，形状 [N, 1]
        """
        # GRU前向传播
        gru_out, _ = self.gru(x)  # [N, L, hidden_dim]
        
        # 取最后一个时间步
        last_out = gru_out[:, -1, :]  # [N, hidden_dim]
        
        # 应用dropout
        last_out = self.dropout(last_out)
        
        # 线性层输出
        output = self.head(last_out)  # [N, output_dim]

        if self.demean:
            output = output - output.mean(dim=0, keepdim=True)
        
        return output
