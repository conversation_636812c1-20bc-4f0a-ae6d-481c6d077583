{"cells": [{"cell_type": "code", "execution_count": 1, "id": "1b1537cf", "metadata": {}, "outputs": [], "source": ["import polars as pl\n", "import numpy as np\n", "import os\n", "import pandas as pd\n", "\n", "date_list = pd.read_csv(\"./date_list.csv\", header=None)[0].to_list()\n", "date_list = [str(i) for i in date_list]\n", "\n", "df = pl.read_parquet(\"/home/<USER>/AAA/diskBig/forBt.parquet\")"]}, {"cell_type": "markdown", "id": "5510cfa7", "metadata": {}, "source": ["## read npy"]}, {"cell_type": "code", "execution_count": null, "id": "e54b4538", "metadata": {}, "outputs": [], "source": ["model_name = \"GRU\"\n", "checkpoint_path = f\"/home/<USER>/tslib/projs/stock1m/checkpoints/daysection0810202155\"\n", "\n", "test_pred = []\n", "for fold in sorted(os.listdir(checkpoint_path)):\n", "    test_pred.append(np.load(os.path.join(checkpoint_path, fold, \"test_preds.npy\")))\n", "test_pred = np.concatenate(test_pred, axis=0)  \n", "\n", "\n", "\n", "valid_dn = np.load('/disk4/shared/intern/laiyc/forModel/valid_dn.npy')\n", "valid_dn = valid_dn[720: -100, :]\n", "valid_dn_expanded = valid_dn[..., np.newaxis, np.newaxis]  # 变成 [1680, 6000, 1, 1]\n", "valid_dn_expanded = np.tile(valid_dn_expanded, (1, 1, 241, 1))\n", "\n", "\n", "valid_pred = test_pred * valid_dn_expanded\n", "valid_pred_flatten = valid_pred.reshape(-1)\n", "valid_dn_expanded_flatten = valid_dn_expanded.reshape(-1)\n", "\n", "valid_pred_flatten = valid_pred_flatten[valid_dn_expanded_flatten]\n", "\n", "\n", "pred_df = pl.DataFrame({f\"{model_name}\": valid_pred_flatten})\n", "\n", "pred_df.write_parquet(f\"./model_factor/{model_name}.parquet\")\n", "\n", "\n", "\n", "\n"]}, {"cell_type": "markdown", "id": "5c0ee93a", "metadata": {}, "source": ["## corr"]}, {"cell_type": "code", "execution_count": 2, "id": "63194757", "metadata": {}, "outputs": [], "source": ["factor_cols = ['GRU_demean', 'GRU_SAttn_demean', 'GRU', 'lgb', 'SAttn_GRU_demean']\n", "\n", "lfs = [\n", "    pl.scan_parquet(f\"./model_factor/{factor_col}.parquet\") for factor_col in factor_cols\n", "]\n", "\n", "factor_df = pl.concat(lfs, how=\"horizontal\").collect()"]}, {"cell_type": "code", "execution_count": 4, "id": "10335349", "metadata": {}, "outputs": [{"data": {"text/html": ["<div><style>\n", ".dataframe > thead > tr,\n", ".dataframe > tbody > tr {\n", "  text-align: right;\n", "  white-space: pre-wrap;\n", "}\n", "</style>\n", "<small>shape: (1_521_269_843, 10)</small><table border=\"1\" class=\"dataframe\"><thead><tr><th>date</th><th>hhmm</th><th>symbol</th><th>datetime</th><th>y1</th><th>GRU_demean</th><th>GRU_SAttn_demean</th><th>GRU</th><th>lgb</th><th>SAttn_GRU_demean</th></tr><tr><td>str</td><td>str</td><td>str</td><td>datetime[μs]</td><td>f32</td><td>f32</td><td>f32</td><td>f32</td><td>f32</td><td>f32</td></tr></thead><tbody><tr><td>&quot;20171214&quot;</td><td>&quot;0925&quot;</td><td>&quot;000001.SZ&quot;</td><td>2017-12-14 09:25:00</td><td>-0.019787</td><td>-0.015377</td><td>-0.04943</td><td>-0.047252</td><td>-0.084131</td><td>-0.091959</td></tr><tr><td>&quot;20171214&quot;</td><td>&quot;0930&quot;</td><td>&quot;000001.SZ&quot;</td><td>2017-12-14 09:30:00</td><td>-0.017504</td><td>0.007212</td><td>-0.00139</td><td>-0.044695</td><td>-0.04508</td><td>-0.106389</td></tr><tr><td>&quot;20171214&quot;</td><td>&quot;0931&quot;</td><td>&quot;000001.SZ&quot;</td><td>2017-12-14 09:31:00</td><td>-0.018307</td><td>0.008934</td><td>-0.03639</td><td>-0.041724</td><td>-0.069194</td><td>-0.124479</td></tr><tr><td>&quot;20171214&quot;</td><td>&quot;0932&quot;</td><td>&quot;000001.SZ&quot;</td><td>2017-12-14 09:32:00</td><td>-0.019069</td><td>0.015057</td><td>-0.084951</td><td>-0.040065</td><td>0.008747</td><td>-0.14542</td></tr><tr><td>&quot;20171214&quot;</td><td>&quot;0933&quot;</td><td>&quot;000001.SZ&quot;</td><td>2017-12-14 09:33:00</td><td>-0.017557</td><td>-0.004163</td><td>-0.116071</td><td>-0.079418</td><td>-0.098546</td><td>-0.173881</td></tr><tr><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td></tr><tr><td>&quot;20241118&quot;</td><td>&quot;1455&quot;</td><td>&quot;001376.SZ&quot;</td><td>2024-11-18 14:55:00</td><td>0.022033</td><td>0.014659</td><td>0.029387</td><td>0.008771</td><td>-0.016533</td><td>0.023589</td></tr><tr><td>&quot;20241118&quot;</td><td>&quot;1456&quot;</td><td>&quot;001376.SZ&quot;</td><td>2024-11-18 14:56:00</td><td>0.022033</td><td>0.009046</td><td>0.022647</td><td>0.000512</td><td>-0.016259</td><td>0.010946</td></tr><tr><td>&quot;20241118&quot;</td><td>&quot;1457&quot;</td><td>&quot;001376.SZ&quot;</td><td>2024-11-18 14:57:00</td><td>0.022033</td><td>0.004858</td><td>0.022408</td><td>-0.001617</td><td>-0.039008</td><td>0.012767</td></tr><tr><td>&quot;20241118&quot;</td><td>&quot;1458&quot;</td><td>&quot;001376.SZ&quot;</td><td>2024-11-18 14:58:00</td><td>0.022033</td><td>0.00572</td><td>0.026085</td><td>0.002201</td><td>-0.033129</td><td>0.017138</td></tr><tr><td>&quot;20241118&quot;</td><td>&quot;1459&quot;</td><td>&quot;001376.SZ&quot;</td><td>2024-11-18 14:59:00</td><td>0.020582</td><td>0.008535</td><td>0.028966</td><td>0.009455</td><td>-0.029884</td><td>0.018345</td></tr></tbody></table></div>"], "text/plain": ["shape: (1_521_269_843, 10)\n", "┌──────────┬──────┬───────────┬─────────────┬───┬─────────────┬───────────┬───────────┬────────────┐\n", "│ date     ┆ hhmm ┆ symbol    ┆ datetime    ┆ … ┆ GRU_SAttn_d ┆ GRU       ┆ lgb       ┆ SAttn_GRU_ │\n", "│ ---      ┆ ---  ┆ ---       ┆ ---         ┆   ┆ emean       ┆ ---       ┆ ---       ┆ demean     │\n", "│ str      ┆ str  ┆ str       ┆ datetime[μs ┆   ┆ ---         ┆ f32       ┆ f32       ┆ ---        │\n", "│          ┆      ┆           ┆ ]           ┆   ┆ f32         ┆           ┆           ┆ f32        │\n", "╞══════════╪══════╪═══════════╪═════════════╪═══╪═════════════╪═══════════╪═══════════╪════════════╡\n", "│ 20171214 ┆ 0925 ┆ 000001.SZ ┆ 2017-12-14  ┆ … ┆ -0.04943    ┆ -0.047252 ┆ -0.084131 ┆ -0.091959  │\n", "│          ┆      ┆           ┆ 09:25:00    ┆   ┆             ┆           ┆           ┆            │\n", "│ 20171214 ┆ 0930 ┆ 000001.SZ ┆ 2017-12-14  ┆ … ┆ -0.00139    ┆ -0.044695 ┆ -0.04508  ┆ -0.106389  │\n", "│          ┆      ┆           ┆ 09:30:00    ┆   ┆             ┆           ┆           ┆            │\n", "│ 20171214 ┆ 0931 ┆ 000001.SZ ┆ 2017-12-14  ┆ … ┆ -0.03639    ┆ -0.041724 ┆ -0.069194 ┆ -0.124479  │\n", "│          ┆      ┆           ┆ 09:31:00    ┆   ┆             ┆           ┆           ┆            │\n", "│ 20171214 ┆ 0932 ┆ 000001.SZ ┆ 2017-12-14  ┆ … ┆ -0.084951   ┆ -0.040065 ┆ 0.008747  ┆ -0.14542   │\n", "│          ┆      ┆           ┆ 09:32:00    ┆   ┆             ┆           ┆           ┆            │\n", "│ 20171214 ┆ 0933 ┆ 000001.SZ ┆ 2017-12-14  ┆ … ┆ -0.116071   ┆ -0.079418 ┆ -0.098546 ┆ -0.173881  │\n", "│          ┆      ┆           ┆ 09:33:00    ┆   ┆             ┆           ┆           ┆            │\n", "│ …        ┆ …    ┆ …         ┆ …           ┆ … ┆ …           ┆ …         ┆ …         ┆ …          │\n", "│ 20241118 ┆ 1455 ┆ 001376.SZ ┆ 2024-11-18  ┆ … ┆ 0.029387    ┆ 0.008771  ┆ -0.016533 ┆ 0.023589   │\n", "│          ┆      ┆           ┆ 14:55:00    ┆   ┆             ┆           ┆           ┆            │\n", "│ 20241118 ┆ 1456 ┆ 001376.SZ ┆ 2024-11-18  ┆ … ┆ 0.022647    ┆ 0.000512  ┆ -0.016259 ┆ 0.010946   │\n", "│          ┆      ┆           ┆ 14:56:00    ┆   ┆             ┆           ┆           ┆            │\n", "│ 20241118 ┆ 1457 ┆ 001376.SZ ┆ 2024-11-18  ┆ … ┆ 0.022408    ┆ -0.001617 ┆ -0.039008 ┆ 0.012767   │\n", "│          ┆      ┆           ┆ 14:57:00    ┆   ┆             ┆           ┆           ┆            │\n", "│ 20241118 ┆ 1458 ┆ 001376.SZ ┆ 2024-11-18  ┆ … ┆ 0.026085    ┆ 0.002201  ┆ -0.033129 ┆ 0.017138   │\n", "│          ┆      ┆           ┆ 14:58:00    ┆   ┆             ┆           ┆           ┆            │\n", "│ 20241118 ┆ 1459 ┆ 001376.SZ ┆ 2024-11-18  ┆ … ┆ 0.028966    ┆ 0.009455  ┆ -0.029884 ┆ 0.018345   │\n", "│          ┆      ┆           ┆ 14:59:00    ┆   ┆             ┆           ┆           ┆            │\n", "└──────────┴──────┴───────────┴─────────────┴───┴─────────────┴───────────┴───────────┴────────────┘"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["df = pl.concat([df, factor_df], how=\"horizontal\")\n", "df"]}, {"cell_type": "code", "execution_count": 5, "id": "846d8ca4", "metadata": {}, "outputs": [], "source": ["# group_by datetime, calculate corr between factor and lgb  (spearman)\n", "df = df.filter(pl.col(\"hhmm\").is_in([\"0959\", \"1029\", \"1059\", \"1129\", \"1329\", \"1359\", \"1429\"]))\n"]}, {"cell_type": "code", "execution_count": 6, "id": "c02006f5", "metadata": {}, "outputs": [], "source": ["df = df.fill_nan(0.0)"]}, {"cell_type": "code", "execution_count": null, "id": "ec544b80", "metadata": {}, "outputs": [], "source": ["\n", "\n", "# ---------- 0. 基础设置 ----------\n", "dt_col     = [\"datetime\"]                                    # 截面时间列\n", "n_factors = len(factor_cols)\n", "\n", "\n", "from itertools import combinations\n", "\n", "# ---------- 1. 构造 S<PERSON>man 相关表达式 ----------\n", "# 相关系数对数 = n*(n-1)/2，提前生成表达式列表以走全向量化流水线\n", "corr_exprs = [\n", "    pl.corr(a, b, method=\"spearman\").alias(f\"{a}__{b}\")\n", "    for a, b in combinations(factor_cols, 2)\n", "]\n", "\n", "# ---------- 2. 在每个截面内计算相关向量 ----------\n", "corr_by_date_lazy = (\n", "    df.lazy()\n", "      .group_by(dt_col)\n", "      .agg(corr_exprs)          # 每行是一条日期记录，上三角向量展平成列\n", "      .fill_nan(0.0)\n", ").collect()\n", "\n", "# ---------- 3. 沿时间轴求平均相关 ----------\n", "avg_corr_vec = (\n", "    corr_by_date_lazy\n", "      .select(pl.exclude(dt_col).mean().fill_nan(0.0))  # 对每个列求平均\n", "      .to_dicts()[0]                      # -> {\"f1__f2\": 0.23, ...}\n", ")\n", "\n", "# ---------- 4. 重构为对称相关矩阵 ----------\n", "mat = np.eye(n_factors, dtype=np.float32)\n", "for i in range(n_factors):\n", "    for j in range(i + 1, n_factors):\n", "        v = avg_corr_vec[f\"{factor_cols[i]}__{factor_cols[j]}\"]\n", "        mat[i, j] = mat[j, i] = v\n", "\n"]}, {"cell_type": "code", "execution_count": 9, "id": "4394d42e", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "mat = np.eye(n_factors, dtype=np.float32)\n", "for i in range(n_factors):\n", "    for j in range(i + 1, n_factors):\n", "        v = avg_corr_vec[f\"{factor_cols[i]}__{factor_cols[j]}\"]\n", "        mat[i, j] = mat[j, i] = v"]}, {"cell_type": "code", "execution_count": 10, "id": "6fa74ed3", "metadata": {}, "outputs": [], "source": ["avg_corr_df = (\n", "    pl.DataFrame(mat, schema=factor_cols)\n", "      .with_columns(pl.Series(\"factor\", factor_cols))\n", "      .select([\"factor\", *factor_cols])\n", ")"]}, {"cell_type": "code", "execution_count": 11, "id": "eca4b7c0", "metadata": {}, "outputs": [{"data": {"text/html": ["<div><style>\n", ".dataframe > thead > tr,\n", ".dataframe > tbody > tr {\n", "  text-align: right;\n", "  white-space: pre-wrap;\n", "}\n", "</style>\n", "<small>shape: (5, 6)</small><table border=\"1\" class=\"dataframe\"><thead><tr><th>factor</th><th>GRU_demean</th><th>GRU_SAttn_demean</th><th>GRU</th><th>lgb</th><th>SAttn_GRU_demean</th></tr><tr><td>str</td><td>f32</td><td>f32</td><td>f32</td><td>f32</td><td>f32</td></tr></thead><tbody><tr><td>&quot;GRU_demean&quot;</td><td>1.0</td><td>0.765588</td><td>0.854824</td><td>0.469014</td><td>0.773915</td></tr><tr><td>&quot;GRU_SAttn_demean&quot;</td><td>0.765588</td><td>1.0</td><td>0.716183</td><td>0.416678</td><td>0.748505</td></tr><tr><td>&quot;GRU&quot;</td><td>0.854824</td><td>0.716183</td><td>1.0</td><td>0.485221</td><td>0.73201</td></tr><tr><td>&quot;lgb&quot;</td><td>0.469014</td><td>0.416678</td><td>0.485221</td><td>1.0</td><td>0.445538</td></tr><tr><td>&quot;SAttn_GRU_demean&quot;</td><td>0.773915</td><td>0.748505</td><td>0.73201</td><td>0.445538</td><td>1.0</td></tr></tbody></table></div>"], "text/plain": ["shape: (5, 6)\n", "┌──────────────────┬────────────┬──────────────────┬──────────┬──────────┬──────────────────┐\n", "│ factor           ┆ GRU_demean ┆ GRU_SAttn_demean ┆ GRU      ┆ lgb      ┆ SAttn_GRU_demean │\n", "│ ---              ┆ ---        ┆ ---              ┆ ---      ┆ ---      ┆ ---              │\n", "│ str              ┆ f32        ┆ f32              ┆ f32      ┆ f32      ┆ f32              │\n", "╞══════════════════╪════════════╪══════════════════╪══════════╪══════════╪══════════════════╡\n", "│ GRU_demean       ┆ 1.0        ┆ 0.765588         ┆ 0.854824 ┆ 0.469014 ┆ 0.773915         │\n", "│ GRU_SAttn_demean ┆ 0.765588   ┆ 1.0              ┆ 0.716183 ┆ 0.416678 ┆ 0.748505         │\n", "│ GRU              ┆ 0.854824   ┆ 0.716183         ┆ 1.0      ┆ 0.485221 ┆ 0.73201          │\n", "│ lgb              ┆ 0.469014   ┆ 0.416678         ┆ 0.485221 ┆ 1.0      ┆ 0.445538         │\n", "│ SAttn_GRU_demean ┆ 0.773915   ┆ 0.748505         ┆ 0.73201  ┆ 0.445538 ┆ 1.0              │\n", "└──────────────────┴────────────┴──────────────────┴──────────┴──────────┴──────────────────┘"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["avg_corr_df"]}, {"cell_type": "code", "execution_count": null, "id": "5a4b8a46", "metadata": {}, "outputs": [{"ename": "", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31m在当前单元格或上一个单元格中执行代码时 Kernel 崩溃。\n", "\u001b[1;31m请查看单元格中的代码，以确定故障的可能原因。\n", "\u001b[1;31m单击<a href='https://aka.ms/vscodeJupyterKernelCrash'>此处</a>了解详细信息。\n", "\u001b[1;31m有关更多详细信息，请查看 Jupyter <a href='command:jupyter.viewOutput'>log</a>。"]}], "source": ["avg_corr_df.write_parquet(\"corr_df.parquet\")"]}, {"cell_type": "markdown", "id": "e9522a1b", "metadata": {}, "source": ["## bt"]}, {"cell_type": "code", "execution_count": 6, "id": "bc23aad5", "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "分钟频因子回测类\n", "用于评估分钟级Alpha因子的表现\n", "\"\"\"\n", "\n", "import polars as pl\n", "import numpy as np\n", "import json\n", "import time\n", "from pathlib import Path\n", "from typing import Dict, Any, Optional, List\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "import matplotlib.pyplot as plt\n", "import matplotlib.patches as mpatches\n", "from matplotlib.backends.backend_pdf import PdfPages\n", "import seaborn as sns\n", "# 使用更简洁的样式，不预设网格线\n", "sns.set_style(\"white\")\n", "# 设置字体\n", "plt.rcParams['font.sans-serif'] = ['DejaVu Sans']\n", "plt.rcParams['axes.unicode_minus'] = False\n", "\n", "import gc\n", "\n", "class MinuteFactorBacktest:\n", "    \"\"\"分钟频因子回测类\"\"\"\n", "    \n", "    def __init__(self, data_dir: str = \"diskBig\", save_dir: str = \"/disk4/shared/intern/laiyc/alpha\", mode: str = \"expr\"):\n", "        \"\"\"\n", "        初始化回测类\n", "        \n", "        参数:\n", "            data_dir: 数据目录路径\n", "        \"\"\"\n", "        self.data_dir = Path(data_dir)\n", "        self.save_dir = Path(save_dir)\n", "        self.mode = mode\n", "        self.specific_times = [\"0959\", \"1029\", \"1059\", \"1129\", \"1329\", \"1359\", \"1429\"]\n", "        \n", "        if mode == \"expr\":\n", "            # 加载数据\n", "            print(\"正在加载数据...\")\n", "            self.df_ohlc = pl.read_parquet(self.data_dir / \"OHLCVA_Vwap.parquet\")\n", "            self.df_y = pl.read_parquet(self.data_dir / \"y1.parquet\")\n", "            \n", "            # 拼接数据\n", "            self.df = self.df_ohlc.hstack(self.df_y)\n", "            print(f\"数据加载完成，总行数: {self.df.shape[0]:,}\")\n", "\n", "        \n", "    \n", "    def backtest_factor(self, factor_expr_or_df: pl.Expr | List[pl.Expr] | pl.<PERSON><PERSON><PERSON>e, factor_name: str='factor') -> Dict[str, Any]:\n", "        \"\"\"\n", "        回测单个因子\n", "\n", "        参数:\n", "            factor_expr_or_df: 因子表达式、表达式列表或预计算的因子DataFrame\n", "            factor_name: 因子名称\n", "\n", "        返回:\n", "            回测结果字典\n", "        \"\"\"\n", "        print(f\"\\n开始回测因子: {factor_name}\")\n", "        start_time = time.time()\n", "\n", "        # 1. 根据输入类型处理因子数据\n", "        factor_start = time.time()\n", "\n", "        if isinstance(factor_expr_or_df, pl.DataFrame):\n", "            print(\"直接回测已有因子DataFrame...\")\n", "            # df_with_factor = self.df.hstack(factor_expr_or_df)\n", "            df_with_factor = factor_expr_or_df\n", "            required_cols = [\"date\", \"hhmm\", \"datetime\", \"symbol\", \"y1\", \"factor\"]\n", "            if not all(col in df_with_factor.columns for col in required_cols):\n", "                raise ValueError(f\"数据中缺少必要列: {required_cols}\")\n", "        else:\n", "            # 如果传入的是表达式，计算因子值\n", "            print(\"正在计算因子值...\")\n", "            df_with_factor = self.df\n", "            if isinstance(factor_expr_or_df, list):\n", "                for expr in factor_expr_or_df:\n", "                    df_with_factor = df_with_factor.with_columns(expr)\n", "                df_with_factor = df_with_factor.select([\n", "                    pl.col(\"date\"),\n", "                    pl.col(\"symbol\"),\n", "                    pl.col(\"hhmm\"),\n", "                    pl.col(\"datetime\"),\n", "                    pl.col(\"y1\"),\n", "                    pl.col(factor_expr_or_df[-1].meta.output_name()).alias(\"factor\")\n", "                ])\n", "            else:\n", "                df_with_factor = self.df.with_columns(factor_expr_or_df.alias(\"factor\"))\n", "\n", "        factor_time = time.time() - factor_start\n", "        \n", "        # 2. 计算因子基本信息\n", "        factor_stats = self._calculate_factor_stats(df_with_factor)\n", "        \n", "        # 3. 计算IC相关指标\n", "        print(\"正在计算IC指标...\")\n", "        ic_results = self._calculate_ic_metrics(df_with_factor)\n", "\n", "        # 5. 生成因子分布数据\n", "        distribution_data = self._get_distribution_data(df_with_factor)\n", "\n", "        # 6. 判断因子方向并调整因子值用于十档分析\n", "        if \"error\" not in ic_results and \"global_ic\" in ic_results:\n", "            mean_ic = ic_results[\"global_ic\"][\"mean_ic\"]\n", "            if mean_ic < 0:\n", "                # 反向因子，将因子值取反进行十档分析\n", "                df_with_factor = df_with_factor.with_columns([\n", "                    (-pl.col(\"factor\")).alias(\"factor\")\n", "                ])\n", "\n", "        # 6. 计算十档分组分析\n", "        print(\"正在计算十档分组分析...\")\n", "        decile_analysis = self._calculate_decile_analysis(df_with_factor)\n", "\n", "        # 6. 整合结果\n", "        results = {\n", "            \"factor_name\": factor_name,\n", "            \"test_start_time\": time.strftime(\"%Y-%m-%d %H:%M:%S\"),\n", "            \"factor_computation_time\": factor_time,\n", "            \"total_backtest_time\": time.time() - start_time,\n", "            \"factor_stats\": factor_stats,\n", "            \"ic_results\": ic_results,\n", "            \"distribution_data\": distribution_data,\n", "            \"decile_analysis\": decile_analysis,\n", "        }\n", "\n", "        # 7. save parquet\n", "        if self.mode == \"expr\":\n", "            df_with_factor.select(pl.col(\"factor\").alias(factor_name)).write_parquet(self.save_dir / f\"{factor_name}.parquet\")\n", "        \n", "        print(f\"因子 {factor_name} 回测完成，耗时: {results['total_backtest_time']:.2f}秒\")\n", "        return results\n", "    \n", "    def _calculate_factor_stats(self, df_with_factor: pl.DataFrame) -> Dict[str, Any]:\n", "        \"\"\"计算因子基本统计信息\"\"\"\n", "        stats = df_with_factor.select([\n", "            pl.col(\"factor\").len().alias(\"total_rows\"),\n", "            pl.col(\"factor\").is_nan().sum().alias(\"nan_rows\"),\n", "            pl.col(\"factor\").is_null().sum().alias(\"null_rows\"),\n", "            pl.col(\"factor\").filter(~pl.col(\"factor\").is_nan() & ~pl.col(\"factor\").is_null()).mean().alias(\"mean\"),\n", "            pl.col(\"factor\").filter(~pl.col(\"factor\").is_nan() & ~pl.col(\"factor\").is_null()).std().alias(\"std\"),\n", "            pl.col(\"factor\").min().alias(\"min\"),\n", "            pl.col(\"factor\").max().alias(\"max\"),\n", "            pl.col(\"factor\").median().alias(\"median\"),\n", "            pl.col(\"factor\").quantile(0.25).alias(\"q25\"),\n", "            pl.col(\"factor\").quantile(0.75).alias(\"q75\")\n", "        ]).to_dicts()[0]\n", "        \n", "        # 计算有效值比例\n", "        valid_rows = stats[\"total_rows\"] - stats[\"nan_rows\"] - stats[\"null_rows\"]\n", "        stats[\"valid_rows\"] = valid_rows\n", "        stats[\"valid_ratio\"] = valid_rows / stats[\"total_rows\"] if stats[\"total_rows\"] > 0 else 0\n", "        \n", "        return stats\n", "    \n", "    def _calculate_ic_metrics(self, df_with_factor: pl.DataFrame) -> Dict[str, Any]:\n", "        \"\"\"计算IC相关指标\"\"\"\n", "        # 筛选特定时间点的数据\n", "        df_ic = df_with_factor.filter(pl.col(\"hhmm\").is_in(self.specific_times))\n", "\n", "        # 计算每个时间点的IC\n", "        ic_df = df_ic.drop_nulls([\"factor\", \"y1\"]).group_by([\"datetime\"]).agg([\n", "            pl.col(\"factor\").len().alias(\"sample_size\"),\n", "            pl.corr(\"factor\", \"y1\", method=\"spearman\").alias(\"ic\"),\n", "            pl.col(\"hhmm\").first()\n", "        ]).filter(\n", "            (pl.col(\"ic\").is_not_nan()) &\n", "            (pl.col(\"ic\").is_not_null())\n", "        )\n", "\n", "        if ic_df.height == 0:\n", "            return {\"error\": \"无有效IC数据\"}\n", "\n", "        # 全局IC统计 \n", "        global_ic_stats = ic_df.drop_nulls([\"ic\"]).select([\n", "            pl.col(\"ic\").mean().alias(\"mean_ic\"),\n", "            pl.col(\"ic\").std().alias(\"ic_std\"),\n", "            (pl.col(\"ic\") > 0).mean().alias(\"ic_positive_ratio\"),\n", "            pl.col(\"ic\").len().alias(\"ic_count\"), \n", "            pl.col(\"sample_size\").mean().alias(\"xs_size_mean\")\n", "        ]).to_dicts()[0]\n", "\n", "        # 计算ICIR\n", "        icir = global_ic_stats[\"mean_ic\"] / global_ic_stats[\"ic_std\"] * np.sqrt(244) if global_ic_stats[\"ic_std\"] > 0 else 0\n", "        global_ic_stats[\"icir\"] = icir\n", "\n", "        # 计算月度IC - 生成热力图矩阵格式\n", "        monthly_ic_data = None\n", "        try:\n", "            # 添加年月和日期列\n", "            ic_with_date = ic_df.with_columns([\n", "                pl.col(\"datetime\").dt.strftime(\"%Y-%m\").alias(\"year_month\"),\n", "            ])\n", "\n", "            # 按年月分组计算IC均值\n", "            monthly_grouped = (\n", "                ic_with_date\n", "                .group_by([\"year_month\"])\n", "                .agg(pl.mean(\"ic\").alias(\"mean_ic\"))\n", "            )\n", "\n", "            # 检查是否有足够的数据进行透视\n", "            if monthly_grouped.height > 0:\n", "                # 转换为pandas进行透视操作（Polars的pivot功能有限）\n", "                monthly_pd = monthly_grouped.to_pandas()\n", "                monthly_ic_data = monthly_pd.set_index(\"year_month\")\n", "            else:\n", "                print(\"没有足够的数据生成月度IC热力图\")\n", "\n", "        except Exception as e:\n", "            print(f\"计算月度IC数据时出错: {e}\")\n", "            monthly_ic_data = None\n", "\n", "        # 按时间点分组的IC统计 - 直接在polars中计算\n", "        time_ic_stats = {}\n", "        for time_point in self.specific_times:\n", "            time_ic_df = ic_df.filter(pl.col(\"hhmm\") == time_point)\n", "\n", "            if time_ic_df.height > 0:\n", "                time_stats = time_ic_df.select([\n", "                    pl.col(\"ic\").mean().alias(\"mean_ic\"),\n", "                    pl.col(\"ic\").std().alias(\"ic_std\"),\n", "                    (pl.col(\"ic\") > 0).mean().alias(\"ic_positive_ratio\"),\n", "                    pl.col(\"ic\").len().alias(\"ic_count\")\n", "                ]).to_dicts()[0]\n", "\n", "                # 计算ICIR\n", "                time_stats[\"icir\"] = time_stats[\"mean_ic\"] / time_stats[\"ic_std\"] * np.sqrt(244) if time_stats[\"ic_std\"] > 0 else 0\n", "                time_ic_stats[time_point] = time_stats\n", "            else:\n", "                time_ic_stats[time_point] = {\n", "                    \"mean_ic\": 0, \"ic_std\": 0, \"icir\": 0,\n", "                    \"ic_positive_ratio\": 0, \"ic_count\": 0\n", "                }\n", "\n", "        return {\n", "            \"global_ic\": global_ic_stats,\n", "            \"time_ic_stats\": time_ic_stats,\n", "            \"monthly_ic_data\": monthly_ic_data\n", "        }\n", "    \n", "    def _get_distribution_data(self, df_with_factor: pl.DataFrame) -> Dict[str, Any]:\n", "        \"\"\"获取因子分布数据\"\"\"\n", "        # 采样数据以减少计算量\n", "        sample_size = min(100000, df_with_factor.height)\n", "        df_sample = df_with_factor.sample(sample_size, seed=42)\n", "\n", "        factor_values = df_sample.drop_nulls([\"factor\"])[\"factor\"].to_numpy()\n", "\n", "        if len(factor_values) == 0:\n", "            return {\"error\": \"无有效因子值\"}\n", "\n", "        return {\n", "            \"values\": factor_values.tolist(),\n", "            \"sample_size\": len(factor_values)\n", "        }\n", "\n", "    def _calculate_decile_analysis(self, df_with_factor: pl.DataFrame) -> Dict[str, Any]:\n", "        \"\"\"计算十档分组分析\"\"\"\n", "        # 筛选特定时间点的数据\n", "        df_decile = df_with_factor.filter(pl.col(\"hhmm\").is_in(self.specific_times))\n", "\n", "        # 去除空值\n", "        df_clean = df_decile.drop_nulls([\"factor\", \"y1\"]).drop_nans([\"factor\", \"y1\"])\n", "\n", "        if df_clean.height == 0:\n", "            return {\"error\": \"无有效数据进行十档分析\"}\n", "\n", "        # 计算每个datetime的平均y1（用于计算超额收益）\n", "        df_with_avg_y1 = df_clean.with_columns([\n", "            pl.col(\"y1\").mean().over([\"datetime\"]).alias(\"avg_y1_by_datetime\")\n", "        ])\n", "\n", "        # 按时间点分组，计算因子十分位数\n", "        df_with_decile = df_with_avg_y1.with_columns([\n", "            pl.col(\"factor\").rank(method='random').qcut(10, labels=[f\"{i+1}\" for i in range(10)], allow_duplicates=True).over([\"datetime\"]).alias(\"decile\")\n", "        ])\n", "\n", "        # 计算每个十分位的平均超额收益率（减去datetime平均值）\n", "        decile_returns = df_with_decile.group_by([\"datetime\", \"decile\"]).agg([\n", "            (pl.col(\"y1\") - pl.col(\"avg_y1_by_datetime\")).mean().alias(\"mean_ex_return\"),\n", "            pl.col(\"y1\").count().alias(\"count\")\n", "        ]).sort([\"datetime\", \"decile\"])\n", "\n", "        # 计算累积PnL\n", "        decile_pnl = decile_returns.with_columns([\n", "            (pl.col(\"mean_ex_return\")).cum_sum().over(\"decile\").alias(\"cumulative_pnl\")\n", "        ])\n", "\n", "        # 计算每个十分位的总体统计\n", "        decile_stats = decile_returns.group_by(\"decile\").agg([\n", "            pl.col(\"mean_ex_return\").mean().alias(\"avg_return\"),\n", "            pl.col(\"mean_ex_return\").std().alias(\"return_std\"),\n", "            pl.col(\"count\").sum().alias(\"total_count\")\n", "        ]).sort(\"decile\")\n", "\n", "        return {\n", "            \"decile_returns\": decile_returns.to_pandas(),\n", "            \"decile_pnl\": decile_pnl.to_pandas(),\n", "            \"decile_stats\": decile_stats.to_pandas()\n", "        }\n", "    \n", "    def generate_report(self, results: Dict[str, Any], output_dir: str = \"icBacktestLog\") -> None:\n", "        \"\"\"生成回测报告\"\"\"\n", "        output_path = Path(output_dir)\n", "        json_dir = output_path / \"json\"\n", "        img_dir = output_path / \"img\"\n", "        json_dir.mkdir(parents=True, exist_ok=True)\n", "        img_dir.mkdir(parents=True, exist_ok=True)\n", "\n", "        factor_name = results[\"factor_name\"]\n", "\n", "        # 生成JSON报告\n", "        json_path = json_dir / f\"{factor_name}_report.json\"\n", "        self._generate_json_report(results, json_path)\n", "\n", "        # 生成PDF报告\n", "        pdf_path = img_dir / f\"{factor_name}_report.pdf\"\n", "        self._generate_pdf_report(results, pdf_path)\n", "\n", "        print(f\"报告已生成:\")\n", "        print(f\"  JSON: {json_path}\")\n", "        print(f\"  PDF: {pdf_path}\")\n", "    \n", "    def _generate_pdf_report(self, results: Dict[str, Any], pdf_path: Optional[Path] = None, only_pos=False) -> None:\n", "        \"\"\"生成PDF报告\"\"\"\n", "        fig = None\n", "        gs = None\n", "        \n", "        try:\n", "            # 使用A4比例 (8.27 x 11.69 inches)\n", "            fig = plt.figure(figsize=(8.27, 11.69))\n", "\n", "            # 设置整体布局 - 5行2列\n", "            gs = fig.add_gridspec(5, 2, height_ratios=[0.04, 0.12, 0.6, 0.6, 0.6], width_ratios=[1, 1],\n", "                                hspace=0.6, wspace=0.4)\n", "\n", "            # 标题\n", "            fig.suptitle(f'Factor Backtest Report: {results[\"factor_name\"]}', \n", "                        fontsize=16, fontweight='bold', y=0.92)\n", "\n", "            # 时间信息\n", "            ax_time = fig.add_subplot(gs[0, :])\n", "            self._plot_time_info(ax_time, results)\n", "\n", "            # 基本统计信息\n", "            ax_stats = fig.add_subplot(gs[1, 0])\n", "            self._plot_basic_stats(ax_stats, results)\n", "\n", "            # 全局IC指标\n", "            ax_global_ic = fig.add_subplot(gs[1, 1])\n", "            self._plot_global_ic(ax_global_ic, results)\n", "\n", "            # 因子分布图\n", "            ax_dist = fig.add_subplot(gs[2, 0])\n", "            self._plot_distribution(ax_dist, results)\n", "\n", "            # 分时间点IC趋势图\n", "            ax_trend = fig.add_subplot(gs[2, 1])\n", "            self._plot_ic_trend(ax_trend, results)\n", "\n", "            # 十档收益率分组图\n", "            ax_decile_returns = fig.add_subplot(gs[3, 0])\n", "            self._plot_decile_returns(ax_decile_returns, results)\n", "\n", "            # 月度IC热力图（第五行右侧）\n", "            ax_heatmap = fig.add_subplot(gs[3, 1])\n", "            self._plot_monthly_ic_heatmap(ax_heatmap, results)\n", "\n", "            # 十档PnL曲线\n", "            ax_decile_pnl = fig.add_subplot(gs[4, :])\n", "            self._plot_decile_pnl(ax_decile_pnl, results, only_pos)\n", "\n", "            plt.tight_layout()\n", "\n", "            if pdf_path:\n", "                with PdfPages(pdf_path) as pdf:\n", "                    pdf.savefig(fig, bbox_inches='tight')\n", "                # 保存PDF后立即清理\n", "                plt.close(fig)\n", "            else:\n", "                plt.show()\n", "                # 在<PERSON><PERSON><PERSON>中显示后不立即清理，让图形正常显示\n", "\n", "        except Exception as e:\n", "            print(f\"PDF生成失败: {e}\")\n", "            # 出错时才强制清理\n", "            if fig is not None:\n", "                plt.close(fig)\n", "        finally:\n", "            # 只在非显示模式下进行全局清理\n", "            if pdf_path is not None:\n", "                plt.close('all')\n", "                gc.collect()\n", "\n", "    def _plot_decile_returns(self, ax, results):\n", "        \"\"\"绘制十档收益率分组图\"\"\"\n", "        if \"decile_analysis\" not in results or \"error\" in results[\"decile_analysis\"]:\n", "            ax.text(0.5, 0.5, \"Decile analysis data unavailable\", ha='center', va='center', transform=ax.transAxes)\n", "            ax.set_title(\"Factor Decile Returns\", fontsize=8, fontweight='bold')\n", "            return\n", "\n", "        decile_stats = results[\"decile_analysis\"][\"decile_stats\"]\n", "\n", "        # 绘制柱状图\n", "        bars = ax.bar(range(1, 11), decile_stats[\"avg_return\"],\n", "                     color=['red' if i in [0, 9] else 'lightblue' for i in range(10)],\n", "                     alpha=0.7, edgecolor='black', linewidth=0.5)\n", "\n", "        # 设置标签和标题\n", "        ax.set_ylabel(\"Average Excess Return\", fontsize=8)\n", "        ax.set_title(\"Factor Decile Excess Returns\", fontsize=8, fontweight='bold')\n", "        ax.set_xticks(range(1, 11))\n", "        ax.set_xticklabels([f\"D{i}\" for i in range(1, 11)], fontsize=8, rotation=30)\n", "\n", "        ax.grid(True, alpha=0.6, linestyle='-', linewidth=0.5)\n", "\n", "    def _plot_decile_pnl(self, ax, results, only_pos: bool = False):\n", "        \"\"\"\n", "        绘制十档PnL曲线\n", "        \n", "        参数:\n", "            only_pos: 是否只显示第10组的PnL曲线\n", "        \"\"\"\n", "        if \"decile_analysis\" not in results or \"error\" in results[\"decile_analysis\"]:\n", "            ax.text(0.5, 0.5, \"Decile analysis data unavailable\", ha='center', va='center', transform=ax.transAxes)\n", "            ax.set_title(\"Decile PnL Curves\", fontsize=8, fontweight='bold')\n", "            return\n", "\n", "        decile_pnl = results[\"decile_analysis\"][\"decile_pnl\"]\n", "        decile_returns = results[\"decile_analysis\"][\"decile_returns\"]\n", "\n", "        import pandas as pd\n", "        import matplotlib.dates as mdates\n", "\n", "        # 计算第10组的夏普比率\n", "        decile_10_returns = decile_returns[decile_returns[\"decile\"] == \"10\"][\"mean_ex_return\"]\n", "        if len(decile_10_returns) > 0:\n", "            sharpe_ratio = decile_10_returns.mean() / decile_10_returns.std() if decile_10_returns.std() > 0 else 0\n", "            annualized_sharpe = sharpe_ratio * np.sqrt(244)\n", "        else:\n", "            annualized_sharpe = 0\n", "\n", "        if only_pos:\n", "            # 只绘制第10组的PnL曲线\n", "            decile_data = decile_pnl[decile_pnl[\"decile\"] == \"10\"]\n", "            if len(decile_data) > 0:\n", "                x_values = pd.to_datetime(decile_data[\"datetime\"]) if \"datetime\" in decile_data.columns else range(len(decile_data))\n", "                ax.plot(x_values, decile_data[\"cumulative_pnl\"], \n", "                       label=\"D10\", linewidth=2.5, alpha=0.9, color='red')\n", "        else:\n", "            # 绘制所有组的PnL曲线\n", "            for i, decile in enumerate([f\"{j+1}\" for j in range(10)]):\n", "                decile_data = decile_pnl[decile_pnl[\"decile\"] == decile]\n", "                if len(decile_data) > 0:\n", "                    x_values = pd.to_datetime(decile_data[\"datetime\"]) if \"datetime\" in decile_data.columns else range(len(decile_data))\n", "                    if i in [0, 9]:\n", "                        ax.plot(x_values, decile_data[\"cumulative_pnl\"],\n", "                               label=f\"D{decile}\", linewidth=2.5, alpha=0.9)\n", "                    else:\n", "                        ax.plot(x_values, decile_data[\"cumulative_pnl\"],\n", "                               label=f\"D{decile}\", linewidth=1, alpha=0.6)\n", "\n", "        # 设置x轴为年份格式\n", "        if isinstance(x_values, pd.DatetimeIndex):\n", "            ax.xaxis.set_major_locator(mdates.YearLocator())\n", "            ax.xaxis.set_major_formatter(mdates.DateFormatter('%y'))\n", "            # 添加次要刻度定位器（月份）\n", "            ax.xaxis.set_minor_locator(mdates.MonthLocator())\n", "            ax.tick_params(axis='x', rotation=30, labelsize=8)\n", "        else:\n", "            # 对于非日期数据，设置次要刻度\n", "            from matplotlib.ticker import AutoMinorLocator\n", "            ax.xaxis.set_minor_locator(AutoMinorLocator())\n", "            ax.yaxis.set_minor_locator(AutoMinorLocator())\n", "\n", "        ax.set_ylabel(\"Cumulative PnL\", fontsize=8)\n", "        ax.set_title(\"Decile PnL Curves\", fontsize=8, fontweight='bold')\n", "        if not only_pos:\n", "            ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=8)\n", "\n", "        # 添加网格线\n", "        ax.grid(True, alpha=0.6, which='major', linestyle='-', linewidth=0.5)  # 主要网格线\n", "        ax.grid(True, alpha=0.3, which='minor', linestyle=':', linewidth=0.3)  # 次要网格线，虚线样式\n", "\n", "        # 显示第10组的夏普比率\n", "        sharpe_text = f\"D10 Sharpe: {annualized_sharpe:.3f}\"\n", "        ax.text(0.02, 0.98, sharpe_text, transform=ax.transAxes, fontsize=10,\n", "                verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))\n", "\n", "    \n", "    def _plot_time_info(self, ax, results):\n", "        \"\"\"绘制时间信息\"\"\"\n", "        ax.axis('off')\n", "\n", "        # 时间信息横向排列\n", "        time_text = (f\"Test Start: {results['test_start_time']}    \"\n", "                    f\"Factor Computation: {results['factor_computation_time']:.2f}s    \"\n", "                    f\"Total Backtest: {results['total_backtest_time']:.2f}s\")\n", "\n", "        ax.text(0.5, 0.5, time_text, ha='center', va='center',\n", "               transform=ax.transAxes, fontsize=10, fontweight='bold')\n", "\n", "    def _plot_basic_stats(self, ax, results):\n", "        \"\"\"绘制基本统计信息\"\"\"\n", "        ax.axis('off')\n", "\n", "        stats = results[\"factor_stats\"]\n", "\n", "        # 数据统计信息，横向排列\n", "        stats_data = [\n", "            [\"Total Rows\", f\"{stats['total_rows']:,}\"],\n", "            [\"Valid Rows\", f\"{stats['valid_rows']:,}\"],\n", "            [\"Mean\", f\"{stats['mean']:.4f}\"],\n", "            [\"Std\", f\"{stats['std']:.4f}\"],\n", "            [\"Min\", f\"{stats['min']:.4f}\"],\n", "            [\"Max\", f\"{stats['max']:.4f}\"]\n", "        ]\n", "\n", "        table = ax.table(cellText=stats_data,\n", "                        colLabels=['Metric', 'Value'],\n", "                        cellLoc='center',\n", "                        loc=(0, 0.1, 1, 0.7),  # 调整表格位置：(left, bottom, width, height)\n", "                        colWidths=[0.4, 0.6])\n", "        table.auto_set_font_size(False)\n", "        table.set_fontsize(10)  # 增大字体\n", "        table.scale(1, 1.2)     # 调整表格高度\n", "\n", "\n", "    def _plot_global_ic(self, ax, results):\n", "        \"\"\"绘制全局IC指标\"\"\"\n", "        ax.axis('off')\n", "\n", "        if \"error\" in results[\"ic_results\"]:\n", "            ax.text(0.5, 0.5, f\"IC Error: {results['ic_results']['error']}\",\n", "                   ha='center', va='center', transform=ax.transAxes, fontsize=10)\n", "            return\n", "\n", "        global_ic = results[\"ic_results\"][\"global_ic\"]\n", "\n", "        # 全局IC数据\n", "        ic_data = [\n", "            [\"Mean IC\", f\"{global_ic['mean_ic']:.4f}\"],\n", "            [\"ICIR\", f\"{global_ic['icir']:.4f}\"],\n", "            [\"IC>0 Ratio\", f\"{global_ic['ic_positive_ratio']:.1%}\"],\n", "            [\"IC Count\", f\"{global_ic['ic_count']:,}\"],\n", "            [\"Avg XS Size\", f\"{global_ic['xs_size_mean']:.1f}\"]\n", "        ]\n", "\n", "        table = ax.table(cellText=ic_data,\n", "                        colLabels=['Global IC', 'Value'],\n", "                        cellLoc='center',\n", "                        loc=(0, 0.1, 1, 0.7),  # 调整表格位置\n", "                        colWidths=[0.5, 0.5])\n", "        table.auto_set_font_size(False)\n", "        table.set_fontsize(11)  # 增大字体\n", "        table.scale(1, 1.3)     # 调整表格高度\n", "\n", "        # 突出显示Mean IC - 设置第一行数据为蓝色\n", "        for i in range(2):  # 2列\n", "            cell = table[(1, i)]  # 第一行数据（索引1，因为0是标题）\n", "            if i == 1:  # Value列\n", "                cell.set_text_props(weight='bold', color='blue')\n", "            cell.set_facecolor('#E6F3FF')  # 浅蓝色背景)\n", "\n", "    def _plot_time_ic(self, ax, results):\n", "        \"\"\"绘制分时间点IC指标\"\"\"\n", "        ax.axis('off')\n", "\n", "        if \"error\" in results[\"ic_results\"]:\n", "            ax.text(0.5, 0.5, \"IC Error\", ha='center', va='center', transform=ax.transAxes, fontsize=10)\n", "            return\n", "\n", "        time_stats = results[\"ic_results\"][\"time_ic_stats\"]\n", "\n", "        # 分时间点IC数据\n", "        ic_data = []\n", "        for time_point in self.specific_times:\n", "            if time_point in time_stats:\n", "                stats = time_stats[time_point]\n", "                ic_data.append([\n", "                    time_point,\n", "                    f\"{stats['mean_ic']:.4f}\",\n", "                    f\"{stats['icir']:.4f}\"\n", "                ])\n", "\n", "        if ic_data:\n", "            table = ax.table(cellText=ic_data,\n", "                            colLabels=['Time', 'Mean IC', 'ICIR'],\n", "                            cellLoc='center',\n", "                            loc=(0, 0.1, 1, 0.7),  # 调整表格位置\n", "                            colWidths=[0.3, 0.35, 0.35])\n", "            table.auto_set_font_size(False)\n", "            table.set_fontsize(10)  # 增大字体\n", "            table.scale(1, 1.2)     # 调整表格高度\n", "\n", "    def _plot_distribution(self, ax, results):\n", "        \"\"\"绘制因子分布图\"\"\"\n", "        if \"error\" in results[\"distribution_data\"]:\n", "            ax.text(0.5, 0.5, f\"Distribution Error: {results['distribution_data']['error']}\",\n", "                   ha='center', va='center', transform=ax.transAxes, fontsize=10)\n", "            return\n", "\n", "        values = results[\"distribution_data\"][\"values\"]\n", "        if len(values) == 0:\n", "            ax.text(0.5, 0.5, \"No Valid Data\", ha='center', va='center', transform=ax.transAxes)\n", "            return\n", "\n", "        ax.hist(values, bins=30, alpha=0.7, color='lightblue', edgecolor='navy', linewidth=0.5)\n", "        ax.set_title('Factor Distribution', fontsize=8, fontweight='bold')\n", "        ax.set_xlabel('Factor Value', fontsize=8)\n", "        ax.set_ylabel('Frequency', fontsize=8)\n", "        ax.tick_params(axis='both', which='major', labelsize=9)\n", "        ax.grid(True, alpha=0.6, linestyle='-', linewidth=0.5)\n", "\n", "    def _plot_ic_trend(self, ax, results):\n", "        \"\"\"绘制IC趋势图\"\"\"\n", "        if \"error\" in results[\"ic_results\"]:\n", "            ax.text(0.5, 0.5, f\"IC Error: {results['ic_results']['error']}\",\n", "                   ha='center', va='center', transform=ax.transAxes, fontsize=10)\n", "            return\n", "\n", "        time_stats = results[\"ic_results\"][\"time_ic_stats\"]\n", "\n", "        # 提取时间点和对应的IC、ICIR值\n", "        time_points = []\n", "        mean_ics = []\n", "        icirs = []\n", "\n", "        for time_point in self.specific_times:\n", "            if time_point in time_stats and time_stats[time_point]['ic_count'] > 0:\n", "                time_points.append(time_point)\n", "                mean_ics.append(time_stats[time_point]['mean_ic'])\n", "                icirs.append(time_stats[time_point]['icir'])\n", "\n", "        if not time_points:\n", "            ax.text(0.5, 0.5, \"No IC Trend Data\", ha='center', va='center', transform=ax.transAxes)\n", "            return\n", "\n", "        # 绘制双轴图\n", "        ax2 = ax.twinx()\n", "\n", "        # 绘制Mean IC\n", "        ax.plot(time_points, mean_ics, 'o-', color='blue', linewidth=2, markersize=6, label='Mean IC')\n", "        ax.set_ylabel('Mean IC', color='blue', fontsize=8)\n", "        ax.tick_params(axis='y', labelcolor='blue', labelsize=8)\n", "\n", "        # 绘制ICIR\n", "        ax2.plot(time_points, icirs, 's-', color='red', linewidth=2, markersize=6, label='ICIR')\n", "        ax2.set_ylabel('ICIR', color='red', fontsize=8)\n", "        ax2.tick_params(axis='y', labelcolor='red', labelsize=8)\n", "\n", "        # 添加零线\n", "        ax.axhline(y=0, color='gray', linestyle='--', alpha=0.5)\n", "        ax2.axhline(y=0, color='gray', linestyle='--', alpha=0.5)\n", "\n", "        # 设置x轴\n", "        ax.set_xlabel('Time Points', fontsize=8)\n", "        ax.tick_params(axis='x', labelsize=9)\n", "        ax.set_title('IC Trend by Time Points', fontsize=8, fontweight='bold', pad=10)\n", "\n", "        # 添加图例\n", "        lines1, labels1 = ax.get_legend_handles_labels()\n", "        lines2, labels2 = ax2.get_legend_handles_labels()\n", "        ax.legend(lines1 + lines2, labels1 + labels2, loc='upper left', fontsize=9)\n", "\n", "        ax.grid(True, alpha=0.6, linestyle='-', linewidth=0.5)\n", "\n", "    def _plot_monthly_ic_heatmap(self, ax, results):\n", "        \"\"\"绘制月度 IC 热力图\"\"\"\n", "        ax.clear()  # 保证是干净的坐标轴\n", "\n", "        # 1️⃣ 先检查数据有效性\n", "        ic_res = results.get(\"ic_results\", {})\n", "        if (\"error\" in ic_res) or (ic_res.get(\"monthly_ic_data\") is None):\n", "            ax.axis(\"off\")\n", "            ax.text(0.5, 0.5,\n", "                    \"No monthly-IC data\",\n", "                    ha=\"center\", va=\"center\",\n", "                    transform=ax.transAxes, fontsize=10)\n", "            return\n", "\n", "        # 2️⃣ 整理成 Year×Month 的透视表\n", "        import pandas as pd   # 仅在本函数内部用到\n", "\n", "        monthly_pd = ic_res[\"monthly_ic_data\"].copy()\n", "        # Series → DataFrame（若本身就是 DataFrame 也兼容）\n", "        if isinstance(monthly_pd, pd.Series):\n", "            monthly_pd = monthly_pd.to_frame(\"mean_ic\")\n", "        monthly_pd = monthly_pd.reset_index(names=\"year_month\")\n", "\n", "        # 拆年、月\n", "        monthly_pd[\"year\"]  = pd.to_datetime(monthly_pd[\"year_month\"]).dt.year\n", "        monthly_pd[\"month\"] = pd.to_datetime(monthly_pd[\"year_month\"]).dt.month\n", "\n", "        heatmap_data = (\n", "            monthly_pd\n", "            .pivot(index=\"year\", columns=\"month\", values=\"mean_ic\")\n", "            .sort_index(ascending=False)                # 年份从上往下递减\n", "            .reindex(columns=range(1, 13))              # 确保 1-12 月都在\n", "        )\n", "\n", "        # 3️⃣ 画热力图\n", "        sns.heatmap(\n", "            heatmap_data,\n", "            ax=ax,\n", "            cmap=\"RdBu_r\",\n", "            center=0,             # 0 作为色谱中心\n", "            linewidths=.4,\n", "            linecolor=\"white\",\n", "            cbar_kws={\"shrink\": .8, \"label\": \"Mean IC\"}\n", "        )\n", "\n", "        # 4️⃣ 美化坐标轴\n", "        ax.set_xlabel(\"Month\", fontsize=10)\n", "        ax.set_ylabel(\"Year\",  fontsize=10)\n", "        ax.set_title(\"Monthly IC Heatmap\", fontsize=8, fontweight=\"bold\", pad=12)\n", "        ax.set_xticklabels(ax.get_xticklabels(), rotation=0, fontsize=8)\n", "        ax.set_yticklabels(ax.get_yticklabels(), rotation=0, fontsize=8)\n", "\n", "    def _generate_json_report(self, results: Dict[str, Any], json_path: Path) -> None:\n", "        \"\"\"生成JSON报告\"\"\"\n", "        # 清理不能序列化的数据\n", "        clean_results = results.copy()\n", "\n", "        # 移除分布数据中的大数组，只保留统计信息\n", "        if \"distribution_data\" in clean_results and \"values\" in clean_results[\"distribution_data\"]:\n", "            values = clean_results[\"distribution_data\"][\"values\"]\n", "            clean_results[\"distribution_data\"] = {\n", "                \"sample_size\": clean_results[\"distribution_data\"][\"sample_size\"],\n", "                \"value_count\": len(values),\n", "            }\n", "\n", "        with open(json_path, 'w', encoding='utf-8') as f:\n", "            json.dump(clean_results, f, indent=2, ensure_ascii=False, default=str)\n", "\n", "    def single_backtest_factors(self, factor_num: int, factor_dir: str = \"alpha299_polars\") -> None:\n", "        \"\"\"\n", "        \"\"\"\n", "        factor_path = Path(factor_dir) / f\"factor_{factor_num}.py\"\n", "        if not factor_path.exists():\n", "            print(f\"因子文件不存在: {factor_path}\")\n", "            return {}\n", "\n", "        factor_expr = self._import_factor(factor_path)\n", "        results = self.backtest_factor(factor_expr, f\"factor_{factor_num}\")\n", "        # self.generate_report(results, output_dir)\n", "        return results\n", "\n", "    def batch_backtest_factors(self, factor_dir: str = \"alpha299_polars\",\n", "                              output_dir: str = \"testTmp/alpha299\") -> Dict[str, Any]:\n", "        \"\"\"批量回测所有因子\"\"\"\n", "        factor_path = Path(factor_dir)\n", "        output_path = Path(output_dir)\n", "        output_path.mkdir(exist_ok=True)\n", "\n", "        # 获取所有因子文件\n", "        factor_files = list(factor_path.glob(\"factor_*.py\"))\n", "        factor_files.sort(key=lambda x: int(x.stem.split('_')[1]))\n", "\n", "        print(f\"发现 {len(factor_files)} 个因子文件\")\n", "\n", "        batch_results = {\n", "            \"batch_start_time\": time.strftime(\"%Y-%m-%d %H:%M:%S\"),\n", "            \"total_factors\": len(factor_files),\n", "            \"successful_factors\": 0,\n", "            \"failed_factors\": 0,\n", "            \"factor_results\": {},\n", "            \"summary_stats\": {}\n", "        }\n", "\n", "        for i, factor_file in enumerate(factor_files, 1):\n", "            factor_name = factor_file.stem\n", "            # if factor_name not in ['factor_236', 'factor_267', 'factor_328', 'factor_200', 'factor_300_xs', 'factor_205', 'factor_187', 'factor_190', 'factor_220', 'factor_177', 'factor_147_xs']:\n", "            #     continue\n", "            if factor_name in ['factor_298', 'factor_147', 'factor_259', 'factor_284', 'factor_10', 'factor_30', 'factor_392', 'factor_144_xs', 'factor_259_xs', 'factor_147_xs']:\n", "                continue\n", "            print(f\"\\n[{i}/{len(factor_files)}] 正在处理: {factor_name}\")\n", "\n", "            # 检查是否已经回测过\n", "            pdf_path = output_path / \"img\" / f\"{factor_name}_report.pdf\"\n", "            if pdf_path.exists():\n", "                print(f\"  已经回测过，跳过\")\n", "                continue\n", "\n", "            try:\n", "                # 动态导入因子函数\n", "                factor_expr = self._import_factor(factor_file)\n", "\n", "                # 回测因子\n", "                results = self.backtest_factor(factor_expr, factor_name)\n", "\n", "                # 生成报告\n", "                self.generate_report(results, output_dir)\n", "\n", "                batch_results[\"factor_results\"][factor_name] = results\n", "                batch_results[\"successful_factors\"] += 1\n", "\n", "            except Exception as e:\n", "                print(f\"因子 {factor_name} 回测失败: {str(e)}\")\n", "                batch_results[\"factor_results\"][factor_name] = {\"error\": str(e)}\n", "                batch_results[\"failed_factors\"] += 1\n", "\n", "            finally:\n", "                # 每个因子处理完后强制清理\n", "                plt.close('all')\n", "                gc.collect()\n", "                \n", "                # 每10个因子进行一次深度清理\n", "                if i % 10 == 0:\n", "                    print(f\"  执行深度内存清理 ({i}/{len(factor_files)})\")\n", "                    import matplotlib\n", "                    matplotlib.pyplot.close('all')\n", "                    gc.collect()\n", "\n", "        # 生成汇总统计\n", "        batch_results[\"summary_stats\"] = self._generate_summary_stats(batch_results)\n", "\n", "        # 保存批量结果\n", "        batch_json_path = output_path / \"batch_results.json\"\n", "        with open(batch_json_path, 'w', encoding='utf-8') as f:\n", "            json.dump(batch_results, f, indent=2, ensure_ascii=False, default=str)\n", "\n", "        print(f\"\\n批量回测完成!\")\n", "        print(f\"成功: {batch_results['successful_factors']} 个\")\n", "        print(f\"失败: {batch_results['failed_factors']} 个\")\n", "        print(f\"汇总结果保存至: {batch_json_path}\")\n", "\n", "        return batch_results\n", "\n", "    def _import_factor(self, factor_file: Path) -> pl.Expr:\n", "        \"\"\"动态导入因子函数\"\"\"\n", "        import importlib.util\n", "        import sys\n", "        \n", "        module_name = factor_file.stem\n", "        module_name = module_name.split(\"_\")[:2]\n", "        module_name = \"_\".join(module_name)\n", "        \n", "        try:\n", "            spec = importlib.util.spec_from_file_location(module_name, factor_file)\n", "            module = importlib.util.module_from_spec(spec)\n", "            \n", "            # 临时添加到sys.modules\n", "            sys.modules[module_name] = module\n", "            spec.loader.exec_module(module)\n", "\n", "            # 获取因子函数并调用\n", "            factor_func = getattr(module, module_name)\n", "            factor_expr = factor_func()\n", "            \n", "            return factor_expr\n", "            \n", "        except Exception as e:\n", "            print(f\"导入因子 {module_name} 失败: {e}\")\n", "            raise\n", "        finally:\n", "            # 清理模块引用\n", "            if module_name in sys.modules:\n", "                del sys.modules[module_name]\n", "            if 'module' in locals():\n", "                del module\n", "            if 'spec' in locals():\n", "                del spec\n", "            gc.collect()\n", "\n", "    def _generate_summary_stats(self, batch_results: Dict[str, Any]) -> Dict[str, Any]:\n", "        \"\"\"生成汇总统计\"\"\"\n", "        successful_results = [\n", "            result for result in batch_results[\"factor_results\"].values()\n", "            if \"error\" not in result and \"ic_results\" in result and \"error\" not in result[\"ic_results\"]\n", "        ]\n", "\n", "        if not successful_results:\n", "            return {\"error\": \"无成功的因子结果\"}\n", "\n", "        # 提取IC统计\n", "        mean_ics = [result[\"ic_results\"][\"global_ic\"][\"mean_ic\"] for result in successful_results]\n", "        icirs = [result[\"ic_results\"][\"global_ic\"][\"icir\"] for result in successful_results]\n", "        ic_positive_ratios = [result[\"ic_results\"][\"global_ic\"][\"ic_positive_ratio\"] for result in successful_results]\n", "\n", "        return {\n", "            \"successful_factor_count\": len(successful_results),\n", "            \"mean_ic_stats\": {\n", "                \"mean\": float(np.mean(mean_ics)),\n", "                \"std\": float(np.std(mean_ics)),\n", "                \"min\": float(np.min(mean_ics)),\n", "                \"max\": float(np.max(mean_ics))\n", "            },\n", "            \"icir_stats\": {\n", "                \"mean\": float(np.mean(icirs)),\n", "                \"std\": float(np.std(icirs)),\n", "                \"min\": float(np.min(icirs)),\n", "                \"max\": float(np.max(icirs))\n", "            },\n", "            \"ic_positive_ratio_stats\": {\n", "                \"mean\": float(np.mean(ic_positive_ratios)),\n", "                \"std\": float(np.std(ic_positive_ratios)),\n", "                \"min\": float(np.min(ic_positive_ratios)),\n", "                \"max\": float(np.max(ic_positive_ratios))\n", "            }\n", "        }\n"]}, {"cell_type": "code", "execution_count": 2, "id": "c8ce9db9", "metadata": {}, "outputs": [], "source": ["SAttn_GRU_demean = pl.read_parquet(\"model_factor/SAttn_GRU_demean.parquet\")"]}, {"cell_type": "code", "execution_count": 3, "id": "bb21ece5", "metadata": {}, "outputs": [], "source": ["df = df.hstack(SAttn_GRU_demean)"]}, {"cell_type": "code", "execution_count": 4, "id": "822091af", "metadata": {}, "outputs": [{"data": {"text/html": ["<div><style>\n", ".dataframe > thead > tr,\n", ".dataframe > tbody > tr {\n", "  text-align: right;\n", "  white-space: pre-wrap;\n", "}\n", "</style>\n", "<small>shape: (1_521_269_843, 6)</small><table border=\"1\" class=\"dataframe\"><thead><tr><th>date</th><th>hhmm</th><th>symbol</th><th>datetime</th><th>y1</th><th>SAttn_GRU_demean</th></tr><tr><td>str</td><td>str</td><td>str</td><td>datetime[μs]</td><td>f32</td><td>f32</td></tr></thead><tbody><tr><td>&quot;20171214&quot;</td><td>&quot;0925&quot;</td><td>&quot;000001.SZ&quot;</td><td>2017-12-14 09:25:00</td><td>-0.019787</td><td>-0.091959</td></tr><tr><td>&quot;20171214&quot;</td><td>&quot;0930&quot;</td><td>&quot;000001.SZ&quot;</td><td>2017-12-14 09:30:00</td><td>-0.017504</td><td>-0.106389</td></tr><tr><td>&quot;20171214&quot;</td><td>&quot;0931&quot;</td><td>&quot;000001.SZ&quot;</td><td>2017-12-14 09:31:00</td><td>-0.018307</td><td>-0.124479</td></tr><tr><td>&quot;20171214&quot;</td><td>&quot;0932&quot;</td><td>&quot;000001.SZ&quot;</td><td>2017-12-14 09:32:00</td><td>-0.019069</td><td>-0.14542</td></tr><tr><td>&quot;20171214&quot;</td><td>&quot;0933&quot;</td><td>&quot;000001.SZ&quot;</td><td>2017-12-14 09:33:00</td><td>-0.017557</td><td>-0.173881</td></tr><tr><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td></tr><tr><td>&quot;20241118&quot;</td><td>&quot;1455&quot;</td><td>&quot;001376.SZ&quot;</td><td>2024-11-18 14:55:00</td><td>0.022033</td><td>0.023589</td></tr><tr><td>&quot;20241118&quot;</td><td>&quot;1456&quot;</td><td>&quot;001376.SZ&quot;</td><td>2024-11-18 14:56:00</td><td>0.022033</td><td>0.010946</td></tr><tr><td>&quot;20241118&quot;</td><td>&quot;1457&quot;</td><td>&quot;001376.SZ&quot;</td><td>2024-11-18 14:57:00</td><td>0.022033</td><td>0.012767</td></tr><tr><td>&quot;20241118&quot;</td><td>&quot;1458&quot;</td><td>&quot;001376.SZ&quot;</td><td>2024-11-18 14:58:00</td><td>0.022033</td><td>0.017138</td></tr><tr><td>&quot;20241118&quot;</td><td>&quot;1459&quot;</td><td>&quot;001376.SZ&quot;</td><td>2024-11-18 14:59:00</td><td>0.020582</td><td>0.018345</td></tr></tbody></table></div>"], "text/plain": ["shape: (1_521_269_843, 6)\n", "┌──────────┬──────┬───────────┬─────────────────────┬───────────┬──────────────────┐\n", "│ date     ┆ hhmm ┆ symbol    ┆ datetime            ┆ y1        ┆ SAttn_GRU_demean │\n", "│ ---      ┆ ---  ┆ ---       ┆ ---                 ┆ ---       ┆ ---              │\n", "│ str      ┆ str  ┆ str       ┆ datetime[μs]        ┆ f32       ┆ f32              │\n", "╞══════════╪══════╪═══════════╪═════════════════════╪═══════════╪══════════════════╡\n", "│ 20171214 ┆ 0925 ┆ 000001.SZ ┆ 2017-12-14 09:25:00 ┆ -0.019787 ┆ -0.091959        │\n", "│ 20171214 ┆ 0930 ┆ 000001.SZ ┆ 2017-12-14 09:30:00 ┆ -0.017504 ┆ -0.106389        │\n", "│ 20171214 ┆ 0931 ┆ 000001.SZ ┆ 2017-12-14 09:31:00 ┆ -0.018307 ┆ -0.124479        │\n", "│ 20171214 ┆ 0932 ┆ 000001.SZ ┆ 2017-12-14 09:32:00 ┆ -0.019069 ┆ -0.14542         │\n", "│ 20171214 ┆ 0933 ┆ 000001.SZ ┆ 2017-12-14 09:33:00 ┆ -0.017557 ┆ -0.173881        │\n", "│ …        ┆ …    ┆ …         ┆ …                   ┆ …         ┆ …                │\n", "│ 20241118 ┆ 1455 ┆ 001376.SZ ┆ 2024-11-18 14:55:00 ┆ 0.022033  ┆ 0.023589         │\n", "│ 20241118 ┆ 1456 ┆ 001376.SZ ┆ 2024-11-18 14:56:00 ┆ 0.022033  ┆ 0.010946         │\n", "│ 20241118 ┆ 1457 ┆ 001376.SZ ┆ 2024-11-18 14:57:00 ┆ 0.022033  ┆ 0.012767         │\n", "│ 20241118 ┆ 1458 ┆ 001376.SZ ┆ 2024-11-18 14:58:00 ┆ 0.022033  ┆ 0.017138         │\n", "│ 20241118 ┆ 1459 ┆ 001376.SZ ┆ 2024-11-18 14:59:00 ┆ 0.020582  ┆ 0.018345         │\n", "└──────────┴──────┴───────────┴─────────────────────┴───────────┴──────────────────┘"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["df"]}, {"cell_type": "code", "execution_count": 7, "id": "a95dde99", "metadata": {}, "outputs": [], "source": ["# from minute_factor_backtest import MinuteFactorBacktest\n", "\n", "bt = MinuteFactorBacktest(mode=\"df\")\n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 8, "id": "a6e6b684", "metadata": {}, "outputs": [], "source": ["df = df.rename({\"SAttn_GRU_demean\": \"factor\"})"]}, {"cell_type": "code", "execution_count": 9, "id": "3ed8e3cd", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "开始回测因子: SAttn_GRU_demean\n", "直接回测已有因子DataFrame...\n", "正在计算IC指标...\n", "正在计算十档分组分析...\n", "因子 SAttn_GRU_demean 回测完成，耗时: 30.93秒\n"]}], "source": ["res = bt.backtest_factor(df, \"SAttn_GRU_demean\")"]}, {"cell_type": "code", "execution_count": 10, "id": "9c3cfebf", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 827x1169 with 10 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["bt._generate_pdf_report(res, only_pos=True)"]}, {"cell_type": "code", "execution_count": null, "id": "a3fd4d5b", "metadata": {}, "outputs": [], "source": ["df"]}], "metadata": {"kernelspec": {"display_name": "lyc", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.13"}}, "nbformat": 4, "nbformat_minor": 5}