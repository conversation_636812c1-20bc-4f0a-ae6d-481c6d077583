"""
TreeCubeDataset - 树模型数据集类

将 [F,D,T,N] 立方体扁平化为树模型 tabular 样本，与神经网络 pipeline 对齐。
"""

import numpy as np
from typing import Dict, Optional, Sequence, Tuple

__all__ = ["TreeCubeDataset"]


def _safe_std(std: np.ndarray, eps: float = 1e-6) -> np.ndarray:
    """安全的标准差计算，避免除零和非有限值"""
    return np.where((~np.isfinite(std)) | (std < eps), eps, std)


def _zscore_cs_keep_nan(y: np.ndarray, eps: float = 1e-6) -> np.ndarray:
    """
    截面 Z-Score：沿 N 维归一化，保留原 NaN。
    支持形状 […, N]（前面任意维度）。
    """
    mu = np.nanmean(y, axis=-1, keepdims=True)
    sd = _safe_std(np.nanstd(y, axis=-1, keepdims=True), eps)
    out = (y - mu) / sd
    out[~np.isfinite(y)] = np.nan
    return out


class TreeCubeDataset:
    """
    把 [F,D,T,N] 立方体扁平化为树模型 tabular 样本

    Parameters
    ----------
    X_fdtn : np.ndarray
        原始数据 (可 mmap) 形状 [F,D,T,N]
    n_feat / n_label / n_weight : int
        通道切分规格（与 FieldSpec 保持一致）
    """

    def __init__(
        self,
        X_fdtn: np.ndarray,
        *,
        n_feat: int,
        n_label: int,
        n_weight: int = 0,
    ):
        assert X_fdtn.ndim == 4, "`X_fdtn` shape must be [F,D,T,N]"
        F, D, T, N = X_fdtn.shape
        assert n_feat + n_weight + n_label == F, "channel split mismatch"

        self.X_fdtn  = X_fdtn
        self.n_feat  = int(n_feat)
        self.n_label = int(n_label)
        self.n_weight = int(n_weight)

        self.F, self.D, self.T, self.N = F, D, T, N

    def flatten(
        self,
        *,
        drop_nan_y: bool = True,
        day_idx: Optional[Sequence[int]] = None,
    ) -> Dict[str, np.ndarray]:
        """
        将 (可选子集 day_idx) 扁平化成表格

        Returns
        -------
        dict { "X":[M,n_feat], "y":[M], "d":[M], "t":[M], "n":[M] }
        """
        # -------- 选择天维 ------------
        if day_idx is None:
            day_idx = np.arange(self.D, dtype=np.int64)
        else:
            day_idx = np.asarray(day_idx, dtype=np.int64)
        Dp = len(day_idx)

        # -------- 切片视图 [F,D',T,N] -> [D',T,N,F] ------------
        X_sub  = self.X_fdtn[:, day_idx[0]:day_idx[-1]+1, :, :]                 # view 或 mmap slice
        # X_sub  = self.X_fdtn[:, day_idx, :, :]
        X_dtnf = np.moveaxis(X_sub, (0,1,2,3), (3,0,1,2))      # [D',T,N,F]

        # -------- 特征、标签切块 ----------
        print("start split")
        f = X_dtnf[..., : self.n_feat]                         # [D',T,N,Ff]
        y = X_dtnf[..., self.n_feat + self.n_weight : self.n_feat + self.n_weight + self.n_label]
        y = y[..., 0]                                           # 折叠 label 通道 -> [D',T,N]

        # -------- 标签截面 z-score ----------
        # 先转到 [D'*T, N] 做一次性向量化
        print("start zscore")
        y_flat_tmp = y.reshape(-1, self.N)                     # [D'*T , N]
        print(f"y_flat_tmp shape: {y_flat_tmp.shape}")
        y_z = _zscore_cs_keep_nan(y_flat_tmp)                  # 标准化后同形
        print(f"y_z shape: {y_z.shape}")
        y_z = y_z.reshape(Dp, self.T, self.N)                  # [D',T,N]

        # -------- reshape 成 tabular ----------
        print("start reshape")
        X_flat = f.reshape(-1, self.n_feat).astype(np.float32)   # [M , n_feat]
        y_flat = y_z.reshape(-1).astype(np.float32)              # [M]

        # -------- 构造 d/t/n without tile/repeat ----------
        # broadcast -> reshape：0 拷贝生成
        print("start broadcast")
        d_grid = np.broadcast_to(day_idx[:, None, None], (Dp, self.T, self.N)).reshape(-1).astype(np.int32)
        t_grid = np.broadcast_to(np.arange(self.T)[None, :, None], (Dp, self.T, self.N)).reshape(-1).astype(np.int32)
        n_grid = np.broadcast_to(np.arange(self.N)[None, None, :], (Dp, self.T, self.N)).reshape(-1).astype(np.int32)

        if drop_nan_y:
            mask = np.isfinite(y_flat)
            print("start return")
            return {
                "X": X_flat[mask],
                "y": y_flat[mask],
                "d": d_grid[mask],
                "t": t_grid[mask],
                "n": n_grid[mask],
            }
        else:
            return {"X": X_flat, "y": y_flat, "d": d_grid, "t": t_grid, "n": n_grid}

    def split_and_flatten(
        self,
        idx_tr: Sequence[int],
        idx_va: Sequence[int],
        idx_te: Sequence[int],
        *,
        drop_nan_y: bool = True,
    ) -> Tuple[Dict[str,np.ndarray], Dict[str,np.ndarray], Dict[str,np.ndarray]]:
        """传入天索引三份，一次性得到三份扁平表格"""
        return (
            self.flatten(drop_nan_y=drop_nan_y, day_idx=idx_tr),
            self.flatten(drop_nan_y=drop_nan_y, day_idx=idx_va),
            self.flatten(drop_nan_y=drop_nan_y, day_idx=idx_te),
        )
