#!/usr/bin/env python3
"""
测试 rolling_global_splits_by_days 函数
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from DLlib.data.folds import rolling_global_splits_by_days, get_global_fold_info


def test_global_splits():
    """测试全局分割函数"""
    D = 1462  # 总天数
    train_days = 180  # 训练窗口
    inner_val_ratio = 0.2  # 验证集比例（4:1 -> 0.2）
    step_days = 180  # 滚动步长
    
    print(f"测试参数:")
    print(f"  总天数: {D}")
    print(f"  训练窗口: {train_days}天")
    print(f"  验证集比例: {inner_val_ratio} (即 {1-inner_val_ratio}:{inner_val_ratio})")
    print(f"  滚动步长: {step_days}天")
    print()
    
    # 获取fold信息
    fold_info = get_global_fold_info(
        D=D,
        train_days=train_days,
        inner_val_ratio=inner_val_ratio,
        step_days=step_days
    )
    
    print("Fold信息统计:")
    for key, value in fold_info.items():
        print(f"  {key}: {value}")
    print()
    
    # 生成并显示前几个fold的详细信息
    folds = list(rolling_global_splits_by_days(
        D=D,
        train_days=train_days,
        inner_val_ratio=inner_val_ratio,
        step_days=step_days
    ))
    
    print(f"生成了 {len(folds)} 个fold")
    print()
    
    # 显示前3个fold的详细信息
    for i, (idx_train, idx_valid, idx_test) in enumerate(folds[:]):
        print(f"Fold {i}:")
        print(f"  训练集: [{idx_train[0]}:{idx_train[-1]+1}] (共{len(idx_train)}天)")
        print(f"  验证集: [{idx_valid[0]}:{idx_valid[-1]+1}] (共{len(idx_valid)}天)")
        print(f"  测试集: [{idx_test[0]}:{idx_test[-1]+1}] (共{len(idx_test)}天)")
        
        # 验证比例
        total_train_window = len(idx_train) + len(idx_valid)
        actual_val_ratio = len(idx_valid) / total_train_window
        print(f"  实际验证集比例: {actual_val_ratio:.3f}")
        print()
    
    # 验证测试集是否都是全局的
    first_test = folds[0][2]
    all_same_test = all(len(fold[2]) == len(first_test) and 
                       (fold[2] == first_test).all() 
                       for fold in folds)
    print(f"所有fold的测试集都相同（全局）: {all_same_test}")
    
    # 验证训练窗口的滚动
    print("\n训练窗口滚动验证:")
    for i in range(len(folds)):
        train_start = folds[i][0][0]
        expected_start = i * step_days
        print(f"  Fold {i}: 训练开始={train_start}, 期望开始={expected_start}, 匹配={train_start == expected_start}")


if __name__ == "__main__":
    test_global_splits()
